{"name": "st-backend", "version": "1.0.0", "description": "", "main": "app.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rm -rf dist && tsc", "start": "node dist/app.js", "start:dev": "npm run build && npm run start -p 4023", "start:watch": "nodemon --watch src -e ts --exec npm run start:dev", "migration:generate": "sh shell/migration_helper.sh", "env:development": "sh -c 'export $(xargs < environment/development.env) && echo $PORT'", "db:create": "npx sequelize-cli db:create --env $ENVIRONMENT", "db:migrate": "npx sequelize-cli db:migrate --env $ENVIRONMENT", "process-quiz-results": "node dist/scheduledTasks/processAssessmentResult.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/bcrypt": "^6.0.0", "@types/bcryptjs": "2.4.6", "@types/express": "5.0.0", "@types/jsonwebtoken": "9.0.8", "@types/node": "22.13.1", "@types/pg": "8.11.11", "@types/sequelize": "4.28.20", "dotenv-cli": "8.0.0", "express": "4.21.2", "nodemon": "3.1.2", "ts-node": "10.9.2", "typescript": "5.7.3"}, "dependencies": {"bash": "^0.0.1", "bcrypt": "^6.0.0", "bcryptjs": "2.4.3", "bignumber.js": "9.1.2", "body-parser": "1.20.3", "caller-callsite": "^5.0.0", "dotenv": "16.4.7", "he": "^1.2.0", "jsonwebtoken": "9.0.2", "pg": "8.11.5", "sequelize": "6.37.5", "sequelize-cli": "6.6.2", "winston": "3.17.0"}}