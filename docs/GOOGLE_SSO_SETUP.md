# Google SSO Setup Guide

This guide explains how to set up Google Single Sign-On (SSO) for your application.

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A project in Google Cloud Console

## Google Cloud Console Setup

### 1. Create OAuth 2.0 Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Navigate to **APIs & Services** > **Credentials**
4. Click **Create Credentials** > **OAuth 2.0 Client IDs**
5. Configure the OAuth consent screen if prompted:
   - Choose **External** for user type
   - Fill in the required fields (App name, User support email, Developer contact)
   - Add your domain to authorized domains if needed
6. For Application type, select **Web application**
7. Add authorized redirect URIs:
   - For development: `http://localhost:4023/api/user/auth/google/callback`
   - For production: `https://yourdomain.com/api/user/auth/google/callback`
8. Click **Create**
9. Copy the **Client ID** and **Client Secret**

### 2. Enable Google+ API (if required)

1. Go to **APIs & Services** > **Library**
2. Search for "Google+ API" or "People API"
3. Click on it and press **Enable**

## Environment Configuration

Update your `environment/development.env` file with the Google OAuth credentials:

```bash
# Google OAuth Configuration
export GOOGLE_CLIENT_ID=your_actual_google_client_id_here
export GOOGLE_CLIENT_SECRET=your_actual_google_client_secret_here
export GOOGLE_CALLBACK_URL=http://localhost:4023/api/user/auth/google/callback
export FRONTEND_URL=http://localhost:3000
```

**Important:** Replace `your_actual_google_client_id_here` and `your_actual_google_client_secret_here` with the actual values from Google Cloud Console.

## Database Migration

Run the database migration to add OAuth fields to the users table:

```bash
npm run db:migrate
```

This will add the following fields to your users table:
- `google_id` - Stores the Google user ID
- `auth_provider` - Enum field ('local' or 'google')
- `profile_picture` - Stores the user's Google profile picture URL
- `refresh_token` - Updated to TEXT type for longer tokens

## API Endpoints

### Initiate Google OAuth
```
GET /api/user/auth/google
```
Redirects the user to Google's OAuth consent screen.

### OAuth Callback
```
GET /api/user/auth/google/callback
```
Handles the OAuth callback from Google and redirects to your frontend with tokens.

## Frontend Integration

### Initiate Google Login
Create a button or link that redirects to:
```
http://localhost:4023/api/user/auth/google
```

### Handle OAuth Callback
Your frontend should handle the callback at:
```
http://localhost:3000/auth/callback?token=ACCESS_TOKEN&refreshToken=REFRESH_TOKEN
```

Extract the tokens from the URL parameters and store them securely.

## User Flow

1. **New User with Google:**
   - User clicks "Login with Google"
   - Redirected to Google OAuth consent screen
   - After consent, a new user account is created
   - User is redirected to frontend with JWT tokens

2. **Existing User (Email Match):**
   - If a user exists with the same email, the Google account is linked
   - User is logged in and redirected with JWT tokens

3. **Returning Google User:**
   - User is directly logged in and redirected with new JWT tokens

## Security Considerations

1. **Environment Variables:** Never commit actual Google credentials to version control
2. **HTTPS:** Use HTTPS in production for OAuth callbacks
3. **Token Storage:** Store JWT tokens securely on the frontend (consider httpOnly cookies)
4. **Scope Limitation:** Only request necessary Google scopes (profile, email)

## Testing

1. Start your backend server: `npm run start:dev`
2. Navigate to: `http://localhost:4023/api/user/auth/google`
3. Complete the Google OAuth flow
4. Verify you're redirected to your frontend with tokens

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" error:**
   - Ensure the callback URL in Google Console exactly matches your environment variable
   - Check for trailing slashes or protocol mismatches

2. **"invalid_client" error:**
   - Verify your Client ID and Client Secret are correct
   - Ensure the OAuth consent screen is properly configured

3. **Database errors:**
   - Run the migration: `npm run db:migrate`
   - Check that all new fields are added to the users table

4. **Frontend not receiving tokens:**
   - Verify the FRONTEND_URL environment variable is correct
   - Check browser network tab for redirect issues

## Production Deployment

1. Update OAuth redirect URIs in Google Console for your production domain
2. Set production environment variables
3. Ensure your production frontend can handle the OAuth callback
4. Test the complete flow in production environment
