# Get User Personalities API

## Overview
This API retrieves a user's personality assessment results based on their latest completed assessment. It returns the top 3 personality types ranked by their assessment scores, along with detailed assessment information.

## Endpoint
`GET /api/assessment/user-personalities`

## Description
Fetches the user's personality results from their most recent completed assessment. The API returns the top 3 personality types with rankings, assessment metadata, and a summary of results.

## Authentication
Requires Bearer token authentication.

## Request Format

### Headers
```
Authorization: Bearer {access_token}
```

### Query Parameters
- `user_id` (number, optional): Get personalities for a specific user. Currently restricted to the authenticated user only.

### Example Requests

#### Get Current User's Personalities
```bash
curl -X GET "{{base_url}}/api/assessment/user-personalities" \
  -H "Authorization: Bearer {{access_token}}"
```

#### Get Specific User's Personalities (Future Feature)
```bash
curl -X GET "{{base_url}}/api/assessment/user-personalities?user_id=123" \
  -H "Authorization: Bearer {{access_token}}"
```

## Response Format

### Success Response - With Results (200)
```json
{
    "success": true,
    "data": {
        "message": "User personalities retrieved successfully",
        "user_id": 123,
        "has_results": true,
        "personalities": {
            "primary": {
                "id": 5,
                "name": "Creative Innovator",
                "rank": 1
            },
            "secondary": [
                {
                    "id": 12,
                    "name": "Analytical Thinker",
                    "rank": 2
                },
                {
                    "id": 8,
                    "name": "Team Collaborator",
                    "rank": 3
                }
            ],
            "all": [
                {
                    "id": 5,
                    "name": "Creative Innovator",
                    "rank": 1
                },
                {
                    "id": 12,
                    "name": "Analytical Thinker",
                    "rank": 2
                },
                {
                    "id": 8,
                    "name": "Team Collaborator",
                    "rank": 3
                }
            ]
        },
        "assessment_info": {
            "assessment_id": 456,
            "completed_at": "2025-08-14T10:30:00.000Z",
            "result_declared_at": "2025-08-14T10:35:00.000Z",
            "total_personalities": 3
        },
        "summary": {
            "total_personalities": 3,
            "primary_personality": "Creative Innovator",
            "assessment_date": "2025-08-14T10:35:00.000Z"
        }
    }
}
```

### Success Response - No Results (200)
```json
{
    "success": true,
    "data": {
        "message": "No assessment results found for user",
        "user_id": 123,
        "has_results": false,
        "personalities": [],
        "assessment_info": null,
        "suggestion": "Complete a personality assessment to see your results"
    }
}
```

## Response Structure

### Personalities Object
- **primary**: The top-ranked personality type (rank 1)
- **secondary**: Array of secondary personality types (ranks 2-3)
- **all**: Complete array of all personality types with rankings

### Personality Type Object
- **id**: Unique identifier for the personality type
- **name**: Human-readable name of the personality type
- **rank**: Ranking position (1 = highest, 2 = second highest, 3 = third highest)

### Assessment Info Object
- **assessment_id**: ID of the assessment that generated these results
- **completed_at**: When the assessment was completed
- **result_declared_at**: When the results were processed and declared
- **total_personalities**: Number of personality types returned

### Summary Object
- **total_personalities**: Count of personality types
- **primary_personality**: Name of the top personality type
- **assessment_date**: Date when results were declared

## Error Responses

### 400 Bad Request - Invalid User ID
```json
{
    "success": false,
    "error": {
        "code": "invalidUserId",
        "message": "Invalid user ID provided"
    }
}
```

### 401 Unauthorized - Missing Token
```json
{
    "success": false,
    "error": {
        "code": "missingUserId",
        "message": "Authentication required"
    }
}
```

### 403 Forbidden - Unauthorized Access
```json
{
    "success": false,
    "error": {
        "code": "unauthorizedUserAccess",
        "message": "Cannot access other user's personality data"
    }
}
```

### 500 Internal Server Error
```json
{
    "success": false,
    "error": {
        "code": "resultRetrievalFailed",
        "message": "Failed to retrieve assessment results"
    }
}
```

## Business Logic

### Result Selection
- Returns results from the **most recent** completed assessment
- Only assessments with `RESULT_DECLARED` status are considered
- Results are ordered by rank (1 = best match, 2 = second best, 3 = third best)

### Personality Ranking
The ranking is based on the assessment result processing algorithm:
1. **Rank 1**: Highest average score across related questions
2. **Rank 2**: Second highest average score
3. **Rank 3**: Third highest average score

### Data Privacy
- Users can only access their own personality results
- Future versions may support admin access or public profile features
- No sensitive assessment data is exposed in the response

## Use Cases

### Frontend Integration
```javascript
// Fetch user personalities
const response = await fetch('/api/assessment/user-personalities', {
    headers: {
        'Authorization': `Bearer ${accessToken}`
    }
});

const data = await response.json();

if (data.success && data.data.has_results) {
    const primaryPersonality = data.data.personalities.primary;
    console.log(`Your primary personality: ${primaryPersonality.name}`);
    
    // Display secondary personalities
    data.data.personalities.secondary.forEach(personality => {
        console.log(`${personality.rank}. ${personality.name}`);
    });
} else {
    console.log('No assessment results found. Please complete an assessment.');
}
```

### Mobile App Integration
- Use the `has_results` flag to determine UI flow
- Display primary personality prominently
- Show secondary personalities as additional insights
- Use assessment date for "last updated" information

## Validation Rules

### Authentication
- Valid Bearer token required
- User must be authenticated

### Authorization
- Users can only access their own results
- Cross-user access returns 403 Forbidden

### Data Validation
- User ID must be a positive integer (if provided)
- Results must exist and be properly formatted
- Personality types must have valid names

## Performance Considerations

### Caching
- Results are relatively static once declared
- Consider caching responses for improved performance
- Cache invalidation when new assessments are completed

### Database Queries
- Single query to get latest assessment result
- Batch query to get personality type names
- Optimized with proper database indexes

## Future Enhancements

### Planned Features
1. **Admin Access**: Allow administrators to view any user's results
2. **Public Profiles**: Optional public sharing of personality results
3. **Historical Results**: Access to previous assessment results
4. **Detailed Insights**: Additional personality insights and descriptions
5. **Comparison Features**: Compare results with friends or averages

### API Versioning
- Current version supports basic personality retrieval
- Future versions may include additional metadata
- Backward compatibility will be maintained

## Testing

### Test Scenarios
1. **Valid Request**: User with completed assessment
2. **No Results**: User without any completed assessments
3. **Invalid Token**: Request without proper authentication
4. **Cross-User Access**: Attempt to access another user's results
5. **Malformed Request**: Invalid user_id parameter

### Expected Behaviors
- Returns 200 with results for users with completed assessments
- Returns 200 with empty results for users without assessments
- Returns 401/403 for authentication/authorization failures
- Handles edge cases gracefully with appropriate error messages
