# Personality Assessment Answer API

## Endpoint
`POST /api/assessment/save-assessment-response`

## Description
This API allows users to submit answers for personality assessment questions. It supports both single answer and multiple answers in a single request.

## Authentication
Requires Bearer token authentication.

## Request Format

### Headers
```
Content-Type: application/json
Authorization: Bearer {access_token}
```

### Request Body

#### Single Answer Format
```json
{
    "assessment_id": "{{assessment_id}}",
    "answers": {
        "question_assessment_set_id": 1,
        "user_answer": 1
    }
}
```

#### Multiple Answers Format (Recommended)
```json
{
    "assessment_id": "{{assessment_id}}",
    "answers": [
        {
            "question_assessment_set_id": 1,
            "user_answer": 1
        },
        {
            "question_assessment_set_id": 2,
            "user_answer": -1
        },
        {
            "question_assessment_set_id": 3,
            "user_answer": 0
        }
    ]
}
```

## Parameters

### assessment_id (string, required)
The ID of the personality assessment.

### answers (object or array, required)
Can be either a single answer object or an array of answer objects.

#### Answer Object Structure
- `question_assessment_set_id` (number, required): The ID of the question in the personality assessment set table
- `user_answer` (integer, required): The user's response as an integer value

## User Answer Values
The `user_answer` field accepts the following integer values:
- `-2`: Strongly disagree
- `-1`: Disagree
- `0`: Neutral
- `1`: Agree
- `2`: Strongly Agree

## Response Format

### Success Response (200)
```json
{
    "success": true,
    "data": {
        "message": "All answers updated successfully",
        "assessment_id": "assessment_123",
        "updated_answers_count": 2,
        "answers": [
            {
                "question_assessment_set_id": 1,
                "user_answer": 1,
                "answer_description": "Agree"
            },
            {
                "question_assessment_set_id": 2,
                "user_answer": -1,
                "answer_description": "Disagree"
            }
        ]
    }
}
```

### Error Responses

#### 400 Bad Request
```json
{
    "success": false,
    "error": {
        "code": "missingAssessmentId",
        "message": "Assessment ID is required"
    }
}
```

#### 401 Unauthorized
```json
{
    "success": false,
    "error": {
        "code": "missingUserId",
        "message": "User authentication required"
    }
}
```

#### 404 Not Found
```json
{
    "success": false,
    "error": {
        "code": "assessmentSetNotFound",
        "message": "Assessment question not found"
    }
}
```

## Validation Rules

1. **assessment_id**: Must be provided and non-empty
2. **answers**: Must be provided as either an object or non-empty array
3. **question_assessment_set_id**: Must be a valid number
4. **user_answer**: Must be an integer between -2 and 2 (inclusive)
5. **Authentication**: Valid Bearer token required
6. **Question Validation**: Each question_assessment_set_id must exist in the specified assessment

## Examples

### Example 1: Single Answer
```bash
curl -X POST "{{base_url}}/api/assessment/save-assessment-response" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {{access_token}}" \
  -d '{
    "assessment_id": "assessment_123",
    "answers": {
        "question_assessment_set_id": 1,
        "user_answer": 2
    }
  }'
```

### Example 2: Multiple Answers
```bash
curl -X POST "{{base_url}}/api/assessment/save-assessment-response" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {{access_token}}" \
  -d '{
    "assessment_id": "assessment_123",
    "answers": [
        {
            "question_assessment_set_id": 1,
            "user_answer": 2
        },
        {
            "question_assessment_set_id": 2,
            "user_answer": -2
        },
        {
            "question_assessment_set_id": 3,
            "user_answer": 0
        }
    ]
  }'
```

## Notes

- The API supports both single answer and multiple answers in one request for flexibility
- Multiple answers in a single request is recommended for better performance
- All answers are validated before any database updates occur
- If any validation fails, no answers will be saved
- The response includes descriptive text for each answer value for better debugging
