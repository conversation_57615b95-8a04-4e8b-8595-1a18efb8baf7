# Assessment Status Management API

## Overview
This unified API allows users to update the status of their assessments. The system ensures that only the assessment owner can modify the status and validates the current state before allowing transitions.

## Assessment Status Values
Based on `AssessmentConstants.ts`:
- `CREATED` (1): Assessment has been created but not started
- `IN_PROGRESS` (2): Assessment has been started by the user
- `COMPLETED` (0): Assessment has been completed by the user
- `LEFT` (4): Assessment was abandoned by the user
- `TIMED_OUT` (5): Assessment timed out
- `RESULT_DECLARED` (6): Assessment results have been declared

---

## Update Assessment Status API

### Endpoint
`POST /api/assessment/update-assessment-status`

### Description
Updates an assessment status based on the provided status parameter. Supports transitioning to both "started" and "completed" states.

### Authentication
Requires Bearer token authentication.

### Request Format

#### Headers
```
Content-Type: application/json
Authorization: Bearer {access_token}
```

#### Request Body

##### Start Assessment
```json
{
    "assessment_id": 123,
    "status": "started"
}
```

##### Complete Assessment
```json
{
    "assessment_id": 123,
    "status": "completed"
}
```

### Parameters
- `assessment_id` (number, required): The ID of the assessment to update
- `status` (string, required): The target status. Accepted values:
  - `"started"` or `"start"`: Updates status to `IN_PROGRESS`
  - `"completed"` or `"complete"`: Updates status to `COMPLETED`

### Validation Rules
1. **Authentication**: Valid Bearer token required
2. **Assessment ID**: Must be a valid number
3. **Status Parameter**: Must be one of the accepted status values
4. **Ownership**: Assessment must belong to the authenticated user
5. **Status Transition**: Must be a valid transition based on current status:
   - `CREATED` → `IN_PROGRESS` (started)
   - `CREATED` → `COMPLETED` (completed)
   - `IN_PROGRESS` → `COMPLETED` (completed)

### Response Format

#### Success Response (200)

##### Start Assessment Response
```json
{
    "success": true,
    "data": {
        "message": "Assessment status updated to started successfully",
        "assessment_id": 123,
        "status": "IN_PROGRESS",
        "status_code": 2,
        "requested_status": "started"
    }
}
```

##### Complete Assessment Response
```json
{
    "success": true,
    "data": {
        "message": "Assessment status updated to completed successfully",
        "assessment_id": 123,
        "status": "COMPLETED",
        "status_code": 0,
        "requested_status": "completed"
    }
}
```

#### Error Responses

##### 400 Bad Request - Invalid Assessment ID
```json
{
    "success": false,
    "error": {
        "code": "invalidAssessmentId",
        "message": "Invalid assessment ID provided"
    }
}
```

##### 400 Bad Request - Invalid Status Parameter
```json
{
    "success": false,
    "error": {
        "code": "invalidStatus",
        "message": "Invalid status parameter provided"
    }
}
```

##### 400 Bad Request - Invalid Status Transition
```json
{
    "success": false,
    "error": {
        "code": "invalidStatusTransition",
        "message": "Invalid status transition"
    }
}
```

##### 403 Forbidden - Not Authorized
```json
{
    "success": false,
    "error": {
        "code": "assessmentNotAuthorized",
        "message": "Assessment does not belong to the authenticated user"
    }
}
```

##### 404 Not Found
```json
{
    "success": false,
    "error": {
        "code": "assessmentNotFound",
        "message": "Assessment not found"
    }
}
```

---

## Security Features

### User Authentication
- Both APIs require valid Bearer token authentication
- User identity is extracted from the token

### Assessment Ownership Validation
- The system verifies that the assessment belongs to the authenticated user
- Cross-user access is prevented with 403 Forbidden response

### Status Transition Validation
- **Start Assessment**: Only allows transition from `CREATED` status
- **Complete Assessment**: Allows transition from `CREATED` or `IN_PROGRESS` status
- Invalid status transitions are rejected with appropriate error messages

---

## Usage Examples

### Example 1: Start Assessment
```bash
curl -X POST "{{base_url}}/api/assessment/update-assessment-status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {{access_token}}" \
  -d '{
    "assessment_id": 123,
    "status": "started"
  }'
```

### Example 2: Complete Assessment
```bash
curl -X POST "{{base_url}}/api/assessment/update-assessment-status" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {{access_token}}" \
  -d '{
    "assessment_id": 123,
    "status": "completed"
  }'
```

---

## Typical Workflow

1. **Assessment Creation**: Assessment is created with `CREATED` status
2. **Start Assessment**: User calls update-assessment-status API with `"status": "started"` to change status to `IN_PROGRESS`
3. **Answer Questions**: User submits answers using the save-assessment-response API
4. **Complete Assessment**: User calls update-assessment-status API with `"status": "completed"` to change status to `COMPLETED`
5. **Result Processing**: System processes results and updates status to `RESULT_DECLARED`

---

## Error Handling

All APIs follow consistent error response format:
- `success`: false for errors
- `error.code`: Machine-readable error code
- `error.message`: Human-readable error message

Common error scenarios:
- Missing or invalid authentication token
- Assessment not found
- Assessment doesn't belong to user
- Invalid status transition
- Database connection issues

---

## Database Impact

The unified API updates the `user_assessments` table:
- Updates the `status` field based on the requested status
- Updates the `updated_at` timestamp
- Maintains audit trail of status changes
- Validates status transitions to ensure data integrity
