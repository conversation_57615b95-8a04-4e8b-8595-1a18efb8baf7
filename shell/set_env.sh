#!/bin/bash

# Check if an argument is provided
if [ -z "$1" ]; then
    echo 'Error: Please provide a valid environment. Options: [dev, staging, prod]'
    exit 1
fi

echo "Setting environment for $1..."

# Function to load environment variables from a file
load_env() {
    local env_file=$1

    if [ ! -f "$env_file" ]; then
        echo "Error: Environment file '$env_file' does not exist."
        exit 1
    fi

    while IFS= read -r line || [ -n "$line" ]; do
        # Skip empty lines and comments
        if [[ -z "$line" || "$line" =~ ^# ]]; then
            continue
        fi
        # Export only valid key-value pairs
        if [[ "$line" =~ ^[a-zA-Z_][a-zA-Z0-9_]*= ]]; then
            echo "Warning: Skipping invalid line:::: $line"
            export "$line"
        else
            echo "Warning: Skipping invalid line: $line"
        fi
    done < "$env_file"
}

# Set environment based on the first argument
case "$1" in
  dev)
    load_env environment/development.env
    ;;
  staging)
    load_env environment/staging.env
    ;;
  prod)
    load_env environment/production.env
    ;;
  *)
    echo 'Error: Invalid environment. Options: [dev, staging, prod]'
    exit 1
    ;;
esac

echo 'Environment variables set successfully.'

# Debug: Print all exported variables (for testing purposes)
env | grep -E 'DB_|API_|SECRET_'
