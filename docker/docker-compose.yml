version: "3.8"
networks:
    st-network:
        driver: bridge

services:
    st-backend:
        container_name: st-backend
        privileged: true
        build:
            context: ../
            dockerfile: ./docker/dockerfile_dev
        image: st-backend
        command: bash -c "source ./docker/development.env && npm run start:watch"
        ports:
            - "4023:4023"
        depends_on:
            - postgres-st
            - memcached-st
        restart: "on-failure"
        volumes:
            - /Users/<USER>/Documents/PCodeBase/st-backend:/app
            - /Users/<USER>/Documents/PCodeBase/st-backend/node_modules:/app/node_modules
        networks:
            - st-network
        logging:
            driver: "json-file"
            options:
                max-size: "2g"
                max-file: "10"
    postgres-st:
        container_name: postgres-st
        image: postgres
        ports:
            - "5435:5432"
        command: [ "postgres", "-cshared_preload_libraries=pg_stat_statements" ]
        logging:
            driver: "json-file"
            options:
                max-size: "50m"
                max-file: "3"
        environment:
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: root
            POSTGRES_DB: st_development
        networks:
            - st-network
        volumes:
            - ~/.postgres_db_st:/var/lib/postgresql/data
    memcached-st:
        container_name: memcached-st
        image: memcached
        ports:
            - "11215:11211"
        networks:
            - st-network
