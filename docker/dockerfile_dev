FROM node:20-alpine AS builder

# Install build dependencies
RUN apk add --no-cache python3 make g++ git bash

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:20-alpine

# Install only production dependencies
RUN apk add --no-cache nginx bash

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production

# Copy built application from builder stage
COPY --from=builder /app/dist ./dist

# Copy development environment file
COPY docker/development.env ./docker/

# Expose port
EXPOSE 4023

# Start the application with bash
CMD ["/bin/bash", "-c", "source ./docker/development.env && npm run start:watch"]
