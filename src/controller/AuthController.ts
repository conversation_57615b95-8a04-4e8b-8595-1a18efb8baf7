import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import User from "../models/User";

const SECRET_KEY = process.env.JWT_SECRET || "your_secret_key";

export class AuthController {

    public async signup(req: Request, res: Response) {
        try {
            res.status(201).json({ message: "User registered successfully", user: {  } });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: "Server error" });
        }
    }

    public async login (req: Request, res: Response) {
        try {
            // const { email, password } = req.body;
            //
            // // Check if user exists
            // const user = await findUserByEmail(email);
            // if (!user) {
            //     return res.status(400).json({ message: "Invalid credentials" });
            // }
            //
            // // Compare password
            // const isMatch = await bcrypt.compare(password, user.password);
            // if (!isMatch) {
            //     return res.status(400).json({ message: "Invalid credentials" });
            // }
            //
            // // Generate JWT token
            // const token = jwt.sign({ userId: user.id, email: user.email }, SECRET_KEY, {
            //     expiresIn: "1h",
            // });

            res.json({ message: "Login successful", token: "token" });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: "Server error" });
        }
    };

}
