import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import bcrypt from 'bcrypt';
import EducationService, { EducationUpdateData } from "../../services/EducationService";

export default class UpdateUserProfile extends BaseController {
    private userId: number;
    private readonly name?: string;
    private readonly mobileNumber?: string;
    private readonly password?: string;
    private readonly username?: string;
    private readonly education?: EducationUpdateData;

    constructor(params: any) {
        super(params);
        const currentUser = params.customParamsInReq?.currentUser;
        this.userId = currentUser?.userId;
        this.name = params.name;
        this.mobileNumber = params.mobileNumber;
        this.password = params.password;
        this.username = params.username;

        // Extract education data if provided
        if (params.education) {
            this.education = EducationService.validateEducationFields(params.education);
        }
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        if (!classInst.userId) {
            return ResponseHelper.error(['invalidUser'], { code: '401' });
        }

        await classInst.validateParams();
        await classInst.updateUserProfile();

        // Update education details if provided
        if (classInst.education && Object.keys(classInst.education).length > 0) {
            const educationService = new EducationService(classInst.userId, classInst.education);
            const educationResult = await educationService.updateUserEducation();

            if (!educationResult.success) {
                return educationResult;
            }
        }

        return classInst.prepareResponse();
    }

    private async validateParams(): Promise<void> {
        const classInst = this;

        // Check if at least one field is provided for update
        const hasBasicFields = classInst.name || classInst.mobileNumber || classInst.password || classInst.username;
        const hasEducationFields = classInst.education && Object.keys(classInst.education).length > 0;

        if (!hasBasicFields && !hasEducationFields) {
            throw new Error('At least one field (name, mobile_number, password, username, or education) must be provided for update');
        }

        // Validate mobile number format if provided
        if (classInst.mobileNumber) {
            const mobileRegex = /^[0-9]{10,15}$/;
            if (!mobileRegex.test(classInst.mobileNumber)) {
                throw new Error('Invalid mobile number format');
            }

            // Check if another user already takes mobile number
            const dbModels = await PostgresModel.getDbModels();
            const mobileExists = await dbModels.user.checkMobileNumberExists(classInst.mobileNumber, classInst.userId);

            if (mobileExists) {
                throw new Error('Mobile number is already registered with another account');
            }
        }

        // Validate password strength if provided
        if (classInst.password) {
            if (classInst.password.length < 6) {
                throw new Error('Password must be at least 6 characters long');
            }
        }

        // Validate name if provided
        if (classInst.name) {
            if (classInst.name.trim().length < 2) {
                throw new Error('Name must be at least 2 characters long');
            }
        }

        Logger.debug(`UpdateUserProfile::validateParams::Validation completed for user ${classInst.userId}`);
    }

    private async updateUserProfile(): Promise<void> {
        const classInst = this;

        try {
            const updateData: any = {};

            // Add fields to update an object only if they are provided
            if (classInst.name) {
                updateData.name = classInst.name.trim();
            }

            if (classInst.mobileNumber) {
                updateData.mobileNumber = classInst.mobileNumber;
            }

            if (classInst.password) {
                updateData.password = await bcrypt.hash(classInst.password, 10);
            }

            if(classInst.username) {
                updateData.username = classInst.username.toLowerCase();
            }

            const dbModels = await PostgresModel.getDbModels();
            await dbModels.user.updateProfile(classInst.userId, updateData);

            Logger.debug(`UpdateUserProfile::updateUserProfile::Successfully updated user ${classInst.userId}`);
        } catch (error: any) {
            Logger.error(`UpdateUserProfile::updateUserProfile::Error updating user profile: ${error.message}`);
            throw error;
        }
    }

    private prepareResponse(): Success {
        return ResponseHelper.success({
            message: "User profile updated successfully",
            updatedFields: {
                name: !!this.name,
                mobileNumber: !!this.mobileNumber,
                password: !!this.password,
                username: !!this.username,
                education: !!(this.education && Object.keys(this.education).length > 0)
            }
        });
    }
}
