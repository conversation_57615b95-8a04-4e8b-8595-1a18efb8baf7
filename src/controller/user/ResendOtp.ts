import BaseController from "../BaseController";
import { Error, <PERSON> } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import ResponseUtils from "../../lib/utils/ResponseHelper";

export default class ResendOtp extends BaseController {
    private email: string;
    private otpType: 'email_verification' | 'password_reset';
    private otpCode?: string;
    private userId?: number;

    constructor(params: any) {
        super(params);
        this.email = params.email;
        this.otpType = params.otp_type || 'email_verification';
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.generateNewOtp();
        return classInst.prepareResponse();
    }

    private async validateParams(): Promise<void> {
        const classInst = this;

        // Validate email format
        if (!classInst.email) {
            throw new Error('Email is required');
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(classInst.email)) {
            throw new Error('Invalid email format');
        }

        // Validate OTP type
        if (!['email_verification', 'password_reset'].includes(classInst.otpType)) {
            throw new Error('Invalid OTP type');
        }

        // Check if user exists and store user ID for later use
        const dbModels = await PostgresModel.getDbModels();
        const user = await dbModels.user.getUserByEmail(classInst.email);

        if (!user) {
            throw new Error('User not found with this email');
        }

        // Store user ID for OTP generation
        classInst.userId = user.id!;

        // For email verification, check if already verified
        if (classInst.otpType === 'email_verification' && user.verifiedEmail) {
            throw new Error('Email is already verified');
        }

        Logger.debug(`ResendOtp::validateParams::Validation completed for email: ${classInst.email}, type: ${classInst.otpType}`);
    }

    private async generateNewOtp(): Promise<void> {
        const classInst = this;

        try {
            const dbModels = await PostgresModel.getDbModels();
            
            // Generate new OTP using user ID
            const otpResult = await dbModels.otpVerification.generateOtp(classInst.userId!, classInst.otpType);
            
            ResponseUtils.throwIfError(otpResult, "Failed to generate OTP");

            classInst.otpCode = ResponseUtils.getData(otpResult).otpCode;
            
            Logger.debug(`ResendOtp::generateNewOtp::New OTP generated for: ${classInst.email}`);
            
        } catch (error: any) {
            Logger.error(`ResendOtp::generateNewOtp::Error: ${error.message}`);
            throw error;
        }
    }

    private prepareResponse(): Success {
        const classInst = this;

        return ResponseHelper.success({
            otp_sent: true,
            otp_code: classInst.otpCode, // In production, don't return this - send via email
            message: `New OTP sent to ${classInst.email}. Please check your email.`,
            expires_in_minutes: 10
        });
    }
}
