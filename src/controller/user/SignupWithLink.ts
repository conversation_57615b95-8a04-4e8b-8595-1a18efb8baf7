import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import jwt from "jsonwebtoken";
import Logger from "../../lib/Logger";
import ResponseUtils from "../../lib/utils/ResponseHelper";

export default class SignupWithLink extends BaseController {
    private email: string;
    private referralCode?: string;
    private tempToken?: string;
    private otpCode?: string;
    private newUserCreated: boolean = false;
    private accessToken?: string;
    private refreshToken?: string;
    private user?: any;
    private associatedSchoolId?: number;

    constructor(params: any) {
        super(params);
        this.email = params.email;
        this.referralCode = params.referralCode;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.validateReferralCode();
        await classInst.signupWithLink();
        await classInst.generateTokensIfNeeded();
        return await classInst.prepareResponse();
    }

    private async validateParams(): Promise<void> {
        const classInst = this;

        // Validate email format
        if (!classInst.email) {
            throw new Error('Email is required');
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(classInst.email)) {
            throw new Error('Invalid email format');
        }

        Logger.debug(`SignupWithLink::validateParams::Email validation completed for: ${classInst.email}`);
    }

    private async validateReferralCode(): Promise<void> {
        const classInst = this;

        try {
            // If no referral code provided, skip validation
            if (!classInst.referralCode) {
                Logger.debug(`SignupWithLink::validateReferralCode::No referral code provided, skipping validation`);
                return;
            }

            const dbModels = await PostgresModel.getDbModels();

            // Find valid referral code by code string
            const referralCodeResult = await dbModels.referralCode.findByCode(classInst.referralCode);

            if (!referralCodeResult.success || !referralCodeResult.data) {
                throw new Error('Invalid or expired referral code');
            }

            const validCode = referralCodeResult.data;

            // Store the associated school ID
            classInst.associatedSchoolId = validCode.associatedSchoolId;

            Logger.debug(`SignupWithLink::validateReferralCode::Valid referral code found for school: ${classInst.associatedSchoolId}`);

        } catch (error: any) {
            Logger.error(`SignupWithLink::validateReferralCode::Error validating referral code: ${error.message}`);
            throw error;
        }
    }

    private async signupWithLink(): Promise<void> {
        const classInst = this;

        try {
            const dbModels = await PostgresModel.getDbModels();
            const user = await dbModels.user.getUserByEmail(classInst.email);

            if (!user) {
                // Email doesn't exist - create user entry and generate OTP
                await classInst.createUserAndGenerateOtp();
                return;
            }

            // Email exists - store user data and check verification and password status
            classInst.user = user;
            Logger.debug(`SignupWithLink::signupWithLink::User found for email: ${classInst.email}, verifiedEmail: ${user.verifiedEmail}, hasPassword: ${!!user.password}`);

        } catch (error: any) {
            Logger.error(`SignupWithLink::signupWithLink::Error checking email status: ${error.message}`);
            throw error;
        }
    }

    private async createUserAndGenerateOtp(): Promise<void> {
        const classInst = this;

        try {
            const dbModels = await PostgresModel.getDbModels();

            // Create user entry with email and link to school if referral code provided
            const newUser = await dbModels.user.createUserWithEmail(classInst.email, classInst.associatedSchoolId);
            classInst.newUserCreated = true;
            classInst.user = newUser;

            // Generate OTP for email verification using user ID
            const otpResult: Success | Error = await dbModels.otpVerification.generateOtp(newUser.id!, 'email_verification');

            ResponseUtils.throwIfError(otpResult, "Failed to generate OTP");

            classInst.otpCode = ResponseUtils.getData(otpResult).otpCode;

            // Generate temp JWT token for the registration process
            classInst.tempToken = jwt.sign(
                {
                    email: classInst.email,
                    userId: newUser.id,
                    type: 'temp_registration'
                },
                process.env.JWT_SECRET as string,
                { expiresIn: "15m" } // 15 minutes for temp token
            );

            Logger.debug(`SignupWithLink::createUserAndGenerateOtp::Created user and generated OTP for: ${classInst.email}`);

        } catch (error: any) {
            Logger.error(`SignupWithLink::createUserAndGenerateOtp::Error: ${error.message}`);
            throw error;
        }
    }

    private async generateTokensIfNeeded(): Promise<void> {
        const classInst = this;

        try {
            if (!classInst.user) {
                Logger.debug(`SignupWithLink::generateTokensIfNeeded::No user data available, skipping token generation`);
                return;
            }

            // Generate access token (expires in 1 hour)
            classInst.accessToken = jwt.sign(
                {
                    userId: classInst.user.id,
                    email: classInst.user.email,
                    type: 'access'
                },
                process.env.JWT_SECRET as string,
                { expiresIn: "1h" }
            );

            // Generate refresh token (expires in 7 days)
            classInst.refreshToken = jwt.sign(
                {
                    userId: classInst.user.id,
                    email: classInst.user.email,
                    type: 'refresh'
                },
                process.env.JWT_SECRET as string,
                { expiresIn: "7d" }
            );

            // Update user's refresh token in database
            const dbModels = await PostgresModel.getDbModels();
            await dbModels.user.updateProfile(classInst.user.id, {
                refreshToken: classInst.refreshToken
            });

            Logger.debug(`SignupWithLink::generateTokensIfNeeded::Tokens generated successfully for user: ${classInst.user.id}`);

        } catch (error: any) {
            Logger.error(`SignupWithLink::generateTokensIfNeeded::Error generating tokens: ${error.message}`);
            throw error;
        }
    }

    private async prepareResponse(): Promise<Success> {
        const classInst = this;

        // If new user was created, return OTP verification response with tokens
        if (classInst.newUserCreated && classInst.tempToken) {
            const response: any = {
                email_exists: false,
                user_created: true,
                temp_token: classInst.tempToken,
                accessToken: classInst.accessToken,
                refreshToken: classInst.refreshToken,
                user: {
                    id: classInst.user?.id,
                    email: classInst.user?.email,
                    name: classInst.user?.name,
                    verifiedEmail: classInst.user?.verifiedEmail,
                    verifiedNumber: classInst.user?.verifiedNumber,
                    referalCodeId: classInst.user?.referalCodeId
                },
                login_phase: "email_verification_required",
                otp_code: classInst.otpCode, // In production, don't return this - send via email
                message: "User created successfully. Please verify your email with the OTP sent."
            };

            // Add referral code information if used
            if (classInst.referralCode && classInst.associatedSchoolId) {
                response.referral_info = {
                    code_used: classInst.referralCode,
                    linked_to_school_id: classInst.associatedSchoolId,
                    message: "User successfully linked to school via referral code"
                };
            }

            return ResponseHelper.success(response);
        }

        // If temp token exists but no new user created (shouldn't happen)
        if (classInst.tempToken) {
            return ResponseHelper.success({
                email_exists: false,
                temp_token: classInst.tempToken,
                message: "Email not found. Use temp token to proceed with registration."
            });
        }

        // Email exists - need to check user status from database
        return await classInst.prepareExistingUserResponse();
    }

    private async prepareExistingUserResponse(): Promise<Success> {
        const classInst = this;

        try {
            const dbModels = await PostgresModel.getDbModels();
            const user = await dbModels.user.getUserByEmail(classInst.email);

            if (!user) {
                // This shouldn't happen as we already checked, but safety check
                throw new Error('User not found during response preparation');
            }

            // Base response with user data and tokens
            const baseResponse = {
                email_exists: true,
                accessToken: classInst.accessToken,
                refreshToken: classInst.refreshToken,
                user: {
                    id: classInst.user?.id,
                    email: classInst.user?.email,
                    name: classInst.user?.name,
                    verifiedEmail: classInst.user?.verifiedEmail,
                    verifiedNumber: classInst.user?.verifiedNumber
                }
            };

            // Check if email is verified
            if (!user.verifiedEmail) {
                return ResponseHelper.success({
                    ...baseResponse,
                    login_phase: "email_not_verified",
                    message: "Email exists but not verified. Please verify your email first."
                });
            }

            // Email is verified, check if password is set
            if (!user.password || user.password.trim() === '') {
                return ResponseHelper.success({
                    ...baseResponse,
                    login_phase: "generate_password",
                    message: "Email verified but password not set. Please generate a password."
                });
            }

            // Email verified and password exists - user can proceed to login
            return ResponseHelper.success({
                ...baseResponse,
                login_phase: "ready_for_login",
                message: "Email verified and password set. You can proceed to login."
            });

        } catch (error: any) {
            Logger.error(`SignupWithLink::prepareExistingUserResponse::Error: ${error.message}`);
            throw error;
        }
    }
}
