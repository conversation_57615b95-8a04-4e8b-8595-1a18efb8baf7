import BaseController from "../BaseController";
import { Error, <PERSON> } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";

export default class Logout extends BaseController {
    private userId: number;

    constructor(params: any) {
        super(params);
        const currentUser = params.customParamsInReq?.currentUser;
        this.userId = currentUser?.userId;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        if (!classInst.userId) {
            return ResponseHelper.error(['invalidUser'], { code: '401' });
        }

        return await classInst.invalidateRefreshToken();
    }

    private async invalidateRefreshToken(): Promise<Success | Error> {
        const classInst = this;
        const userModel = (await PostgresModel.getDbModels()).user;

        try {
            await userModel.update(
                { refreshToken: null },
                { id: classInst.userId } 
            );

            Logger.info(`Logout-invalidateRefreshToken: Successfully invalidated refresh token for user ${classInst.userId}`);
            return classInst.prepareResponse();
        } catch (error) {
            Logger.error(`Logout-invalidateRefreshToken: Error invalidating refresh token for user ${classInst.userId}. Error: ${error}`);
            return ResponseHelper.error(['internalServerError'], { code: '500' });
        }
    }

    private prepareResponse(): Success {
        return ResponseHelper.success({
            data: {
                message: "Logged out successfully"
            }
        });
    }
} 