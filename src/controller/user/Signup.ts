import BaseController from "../BaseController";
import { <PERSON>rror, <PERSON> } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import bcrypt from "bcryptjs";
import Logger from "../../lib/Logger";
import { UserModelAttributes } from "../../lib/types/Model";


export default class Signup extends BaseController {
    private name: string;
    private password: string;
    private email: string;
    private username: string;
    private mobileNumber: string;

    constructor(params: any) {
        super(params);

        const classInst = this;
        Logger.info(`Signup::constructor::Params: ${JSON.stringify(params)}`)
        classInst.name = params.name
        classInst.password = params.password
        classInst.email = params.email
        classInst.username = params.username
        classInst.mobileNumber = params.mobileNumber
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.createUser();
        return classInst.prepareResponse();
    }

    private async validateParams() {
        // Verify username is unique
        // Verify email is unique
        // Verify name is not empty
        // Password strength
        Logger.info(`Signup::validateParams::Validating Params.`)
    }

    private async createUser() {
        const classInst = this;
        const registeringUserObject: UserModelAttributes = {
            id: undefined,
            name: classInst.name,
            username: classInst.username.toLowerCase(),
            email: classInst.email,
            password: await bcrypt.hash(classInst.password, 10),
            mobileNumber: classInst.mobileNumber
        }
        const userModel = (await PostgresModel.getDbModels()).user
        await userModel.create(registeringUserObject)
    }

    private prepareResponse(): Success {
        const classInst = this;
        return ResponseHelper.success({
            redirectTo: "home"
        });
    }
}
