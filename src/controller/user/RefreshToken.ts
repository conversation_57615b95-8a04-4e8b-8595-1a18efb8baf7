import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import jwt from "jsonwebtoken";
import Logger from "../../lib/Logger";

export default class RefreshToken extends BaseController {
    private refreshToken: string;
    private accessToken: string;
    private userId: number;

    constructor(params: any) {
        super(params);
        this.refreshToken = params.refreshToken;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateRefreshToken();
        await classInst.generateNewAccessToken();
        return classInst.prepareResponse();
    }

    private async validateRefreshToken() {
        const classInst = this;
        const userModel = (await PostgresModel.getDbModels()).user;

        try {
            const decoded = jwt.verify(classInst.refreshToken, process.env.JWT_SECRET as string) as { userId: number };
            classInst.userId = decoded.userId;

            const user = await userModel.findByPk(decoded.userId);

            if (!user || user.refreshToken !== classInst.refreshToken) {
                return classInst.unauthorizedResponse("invalidRefreshToken", "401");
            }
        } catch (error) {
            return classInst.unauthorizedResponse("invalidRefreshToken", "401");
        }
    }

    private async generateNewAccessToken() {
        const classInst = this;
        const userModel = (await PostgresModel.getDbModels()).user;
        const user = await userModel.findByPk(classInst.userId);

        if (user) {
            classInst.accessToken = jwt.sign(
                { userId: user.id, email: user.email, username: user.username },
                process.env.JWT_SECRET as string,
                { expiresIn: "1h" }
            );
        }
    }

    private prepareResponse(): Success {
        const classInst = this;

        return ResponseHelper.success({
            accessToken: classInst.accessToken,
            message: "Token refreshed successfully"
        });
    }
}
