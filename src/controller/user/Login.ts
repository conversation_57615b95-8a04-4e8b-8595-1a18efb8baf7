import BaseController from "../BaseController";
import { <PERSON>rror, <PERSON> } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import GenericOpHelpler from "../../lib/utils/GenericOpHelpler";
import Logger from "../../lib/Logger";


export default class Login extends BaseController {
    private name: string;
    private password: string;
    private email: string;
    private username: string;
    private accessToken: string;
    private refreshToken: string;
    private mobileNumber: string;
    private refCode: string;

    constructor(params: any) {
        super(params);

        const classInst = this;
        classInst.name = params.name
        classInst.refCode = params.refCode
        classInst.password = params.password
        classInst.email = params.email
        classInst.username = params.username
        classInst.mobileNumber = params.mobileNumber
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.getUserByEmailOrUsername();
        return classInst.prepareResponse();
    }

    private async validateParams() {
        /**
         TODO:
         Verify username is unique
         Verify email is unique
         Verify name is not empty
         Password strength
         */
    }

    private async getUserByEmailOrUsername() {
        const classInst = this;
        const userModel = (await PostgresModel.getDbModels()).user
        const userDetails = await userModel.getUserByEmailOrUsername(classInst.email, classInst.username?.toLowerCase())
        Logger.debug(`Login-getUserByEmailOrUsername: User details: ${JSON.stringify(userDetails)}`)
        if (!userDetails) {
            // TODO: Call sign up flow.
            return;
        }
        const isPasswordValid = await bcrypt.compare(classInst.password, userDetails.password);
        if (!isPasswordValid) {
            return classInst.unauthorizedResponse("invalidUser", "404");
        }

        // Generate Access Token
        classInst.accessToken = jwt.sign(
            { userId: userDetails.id, email: userDetails.email, username: userDetails?.username },
            process.env.JWT_SECRET as string,
            { expiresIn: "1h" }
        );

        // Generate Refresh Token
        classInst.refreshToken = jwt.sign(
            { userId: userDetails.id },
            process.env.JWT_SECRET as string,
            { expiresIn: "7d" }
        );

        // Store refresh token in database
        await userModel.update(
            { refreshToken: classInst.refreshToken },
            { id: userDetails.id }
        );

        Logger.info(`Login-getUserByEmailOrUsername: Generated tokens for user ${userDetails.id}`);
    }

    private prepareResponse(): Success {
        const classInst = this;

        return ResponseHelper.success({
            redirectTo: "home",
            accessToken: classInst.accessToken,
            refreshToken: classInst.refreshToken,
            message: "Login successful"
        });
    }
}
