import BaseController from "../BaseController";
import { Error, <PERSON> } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import jwt from "jsonwebtoken";
import Logger from "../../lib/Logger";
import ResponseUtils from "../../lib/utils/ResponseHelper";

export default class VerifyOtp extends BaseController {
    private email: string;
    private otpCode: string;
    private otpType: 'email_verification' | 'password_reset';
    private userId?: number;
    private accessToken?: string;
    private refreshToken?: string;
    private user?: any;

    constructor(params: any) {
        super(params);
        this.email = params.email;
        this.otpCode = params.otp_code;
        this.otpType = params.otp_type || 'email_verification';
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.verifyOtpCode();
        await classInst.generateTokens();
        return classInst.prepareResponse();
    }

    private async validateParams(): Promise<void> {
        const classInst = this;

        // Validate email format
        if (!classInst.email) {
            throw new Error('Email is required');
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(classInst.email)) {
            throw new Error('Invalid email format');
        }

        // Validate OTP code
        if (!classInst.otpCode) {
            throw new Error('OTP code is required');
        }

        if (!/^\d{6}$/.test(classInst.otpCode)) {
            throw new Error('OTP code must be 6 digits');
        }

        // Validate OTP type
        if (!['email_verification', 'password_reset'].includes(classInst.otpType)) {
            throw new Error('Invalid OTP type');
        }

        Logger.debug(`VerifyOtp::validateParams::Validation completed for email: ${classInst.email}, type: ${classInst.otpType}`);
    }

    private async verifyOtpCode(): Promise<void> {
        const classInst = this;

        try {
            const dbModels = await PostgresModel.getDbModels();

            // Get user by email to get user ID
            const user = await dbModels.user.getUserByEmail(classInst.email);
            if (!user) {
                throw new Error('User not found with this email');
            }

            classInst.userId = user.id!;
            classInst.user = user;

            // Verify the OTP using user ID
            const verificationResult: Success | Error = await dbModels.otpVerification.verifyOtp(
                classInst.userId,
                classInst.otpCode,
                classInst.otpType
            );

            ResponseUtils.throwIfError(verificationResult, "OTP verification failed");

            // If email verification, mark user's email as verified
            if (classInst.otpType === 'email_verification') {
                await dbModels.user.updateProfile(user.id!, { verifiedEmail: true });
                Logger.debug(`VerifyOtp::verifyOtpCode::Email verified for user: ${user.id}`);
            }

            Logger.debug(`VerifyOtp::verifyOtpCode::OTP verified successfully for userId: ${classInst.userId}`);

        } catch (error: any) {
            Logger.error(`VerifyOtp::verifyOtpCode::Error verifying OTP: ${error.message}`);
            throw error;
        }
    }

    private async generateTokens(): Promise<void> {
        const classInst = this;

        try {
            if (!classInst.user) {
                throw new Error('User data not available for token generation');
            }

            // Generate access token (expires in 1 hour)
            classInst.accessToken = jwt.sign(
                {
                    userId: classInst.user.id,
                    email: classInst.user.email,
                    type: 'access'
                },
                process.env.JWT_SECRET as string,
                { expiresIn: "1h" }
            );

            // Generate refresh token (expires in 7 days)
            classInst.refreshToken = jwt.sign(
                {
                    userId: classInst.user.id,
                    email: classInst.user.email,
                    type: 'refresh'
                },
                process.env.JWT_SECRET as string,
                { expiresIn: "7d" }
            );

            // Update user's refresh token in database
            const dbModels = await PostgresModel.getDbModels();
            await dbModels.user.updateProfile(classInst.user.id, {
                refreshToken: classInst.refreshToken
            });

            Logger.debug(`VerifyOtp::generateTokens::Tokens generated successfully for user: ${classInst.user.id}`);

        } catch (error: any) {
            Logger.error(`VerifyOtp::generateTokens::Error generating tokens: ${error.message}`);
            throw error;
        }
    }

    private prepareResponse(): Success {
        const classInst = this;

        const baseResponse = {
            verified: true,
            accessToken: classInst.accessToken,
            refreshToken: classInst.refreshToken,
            user: {
                id: classInst.user?.id,
                email: classInst.user?.email,
                name: classInst.user?.name,
                verifiedEmail: classInst.user?.verifiedEmail,
                verifiedNumber: classInst.user?.verifiedNumber
            }
        };

        if (classInst.otpType === 'email_verification') {
            return ResponseHelper.success({
                ...baseResponse,
                login_phase: "email_verified",
                message: "Email verified successfully. You can now access your account."
            });
        } else {
            return ResponseHelper.success({
                ...baseResponse,
                login_phase: "password_reset_verified",
                message: "OTP verified successfully. You can now reset your password."
            });
        }
    }
}
