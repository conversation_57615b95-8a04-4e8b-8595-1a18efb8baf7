
import Logger from '../lib/Logger';
import { Error, Success } from "../lib/types/Response";
import ResponseHelper from "../middlewares/ResponseHelper";
import APIParamValidator from "../middlewares/APIParamsValidator";

/**
 * Base class for all services.
 *
 * @class BaseController
 */
export default class BaseController {
    private params: any;

    /**
     * Constructor for base class for all services.
     *
     * @constructor
     */
    constructor(params: any) {
        const oThis = this;
        Logger.debug(`Base parameters: ${JSON.stringify(params)}`);

        oThis.params = params;
    }

    /**
     * Main performer for class.
     *
     * @returns {Promise<*>}
     */
    async perform(): Promise<Success | Error> {
        const oThis = this;

        const apiParamValidationResponse = await new APIParamValidator({
            apiName: oThis.params.customParamsInReq.apiName,
            externalParams: oThis.params,
            internalParams: oThis.params.customParamsInReq,
        }).perform();

        if (!apiParamValidationResponse.success) {
            return apiParamValidationResponse;
        }

        return oThis.servicePerform().catch(async (err) => {
            Logger.error(`ServiceBase::perform::In catch block of services/Base.js Error is ${JSON.stringify(err.message)}, stackTrace: ${JSON.stringify(err.stack)}`);
            return {
                success: false,
                errorData: "Something went wrong"
            };
        });
    }


    /**
     * Async perform.
     *
     * @private
     * @returns {Promise<void>}
     */
    public async servicePerform(): Promise<Success | Error> {
        throw new Error('Sub-class to implement.');
    }

    /**
     * Invalid error.
     *
     * @param errorIdentifier
     * @param {string} errorCode
     *
     * @param extraData
     * @returns {Promise<void>}
     */
    protected unauthorizedResponse(errorIdentifier: string, errorCode: string, extraData = {}): Promise<void> {
        const errorCodeObject = { 'errorCode': errorCode };
        const mergedDebugData = { ...errorCodeObject, ...extraData }
        return Promise.reject(
            ResponseHelper.error([errorIdentifier], mergedDebugData),
        );
    }
}
