import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import Logger from "../../lib/Logger";
import AssessmentResultProcessor from "../../scheduledProcessor/assessmentResults";

interface RequestBody {
    assessment_id?: number; // Optional: process specific assessment
    customParamsInReq: any;
}

export default class ProcessAssessmentResults extends BaseController {
    private assessmentId?: number;
    private userId: number;

    constructor(params: RequestBody) {
        super(params);
        const classInst = this;
        Logger.debug(`ProcessAssessmentResults-constructor: Param body: ${JSON.stringify(params)}`)
        Logger.debug(`ProcessAssessmentResults-constructor: User details: ${JSON.stringify(params.customParamsInReq?.currentUser)}`)

        classInst.assessmentId = params.assessment_id;
        classInst.userId = params.customParamsInReq?.currentUser?.id;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();

        if (classInst.assessmentId) {
            await classInst.processSingleAssessment();
        } else {
            await classInst.processAllCompletedAssessments();
        }
        return ResponseHelper.success({
            message: "Assessment result processed successfully",
            assessment_id: classInst.assessmentId,
            processed: true
        });
    }

    private async validateParams() {
        const classInst = this;

        // Validate userId
        if (!classInst.userId) {
            return classInst.unauthorizedResponse("missingUserId", "400");
        }

        // If assessment_id is provided, validate it
        if (classInst.assessmentId && (typeof classInst.assessmentId !== 'number' || classInst.assessmentId <= 0)) {
            return classInst.unauthorizedResponse("invalidAssessmentId", "400");
        }
    }

    private async processSingleAssessment() {
        const classInst = this;

        try {
            const processor = new AssessmentResultProcessor();
            const success = await processor.processAssessmentById(classInst.assessmentId!);

            if (success) {
                return ResponseHelper.success({
                    message: "Assessment result processed successfully",
                    assessment_id: classInst.assessmentId,
                    processed: true
                });
            } else {
                return classInst.unauthorizedResponse("processingFailed", "400");
            }
        } catch (error) {
            Logger.error(`ProcessAssessmentResults-processSingleAssessment: Error processing assessment. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("processingError", "500");
        }
    }

    private async processAllCompletedAssessments() {
        const classInst = this;

        try {
            const processor = new AssessmentResultProcessor();
            await processor.initializeModels();
            await processor.processCompletedAssessments();

            return ResponseHelper.success({
                message: "All completed assessments processed successfully",
                processed_all: true
            });
        } catch (error) {
            Logger.error(`ProcessAssessmentResults-processAllCompletedAssessments: Error processing assessments. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("processingError", "500");
        }
    }
}
