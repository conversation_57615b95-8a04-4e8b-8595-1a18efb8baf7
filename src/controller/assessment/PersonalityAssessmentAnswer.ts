import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import { PersonalityAssessmentSetModelAttributes } from "../../lib/types/Model";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface AssessmentAnswer {
    question_assessment_set_id: number;
    user_answer: number; // -2 = Strongly disagree, -1 = Disagree, 0 = Neutral, 1 = Agree, 2 = Strongly Agree
}

interface RequestBody {
    assessment_id: number;
    answers: AssessmentAnswer | AssessmentAnswer[]; // Support both single answer and array of answers
    customParamsInReq: any;
}

export default class PersonalityAssessmentAnswer extends BaseController {
    private answers: AssessmentAnswer[];
    private assessmentId: number;
    private userId: number;
    private personalityAssessmentSet: any;

    constructor(params: RequestBody) {
        super(params);
        const classInst = this;
        Logger.debug(`PersonalityAssessmentAnswer-constructor: Param body: ${JSON.stringify(params)}`)
        Logger.debug(`PersonalityAssessmentAnswer-constructor: User details: ${JSON.stringify(params.customParamsInReq?.currentUser)}`)

        classInst.assessmentId = params.assessment_id;
        classInst.userId = params.customParamsInReq?.currentUser?.userId;

        // Handle both single answer object and array of answers
        if (Array.isArray(params.answers)) {
            classInst.answers = params.answers;
        } else {
            classInst.answers = [params.answers];
        }
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();
        await classInst.updateAnswers();

        return classInst.prepareResponse();
    }

    private async validateParams() {
        const classInst = this;

        // Validate assessment_id
        if (!classInst.assessmentId) {
            return classInst.unauthorizedResponse("missingAssessmentId", "400");
        }

        // Validate userId
        if (!classInst.userId) {
            return classInst.unauthorizedResponse("missingUserId", "400");
        }

        // Validate answers array
        if (!classInst.answers || !Array.isArray(classInst.answers) || classInst.answers.length === 0) {
            return classInst.unauthorizedResponse("invalidAnswers", "400");
        }

        // Validate each answer
        for (const answer of classInst.answers) {
            if (!answer.question_assessment_set_id || typeof answer.question_assessment_set_id !== 'number') {
                return classInst.unauthorizedResponse("invalidQuestionAssessmentSetId", "400");
            }

            // Validate user_answer is an integer between -2 and 2
            if (typeof answer.user_answer !== 'number' ||
                !Number.isInteger(answer.user_answer) ||
                answer.user_answer < -2 ||
                answer.user_answer > 2) {
                return classInst.unauthorizedResponse("invalidUserAnswer", "400");
            }
        }
    }

    private async updateAnswers() {
        const classInst = this;
        const personalityAssessmentSet = (await PostgresModel.getDbModels()).personalityAssessmentSet;

        try {
            for (const answer of classInst.answers) {
                // Verify the question belongs to the specified assessment
                const assessmentSets = await personalityAssessmentSet.findByAssessmentId(classInst.assessmentId);
                const assessmentSet = assessmentSets.find(qs => qs.id === answer.question_assessment_set_id);

                if (!assessmentSet) {
                    Logger.error(`PersonalityAssessmentAnswer-updateAnswers: Assessment set not found for id ${answer.question_assessment_set_id} in assessment ${classInst.assessmentId}`);
                    return classInst.unauthorizedResponse("assessmentSetNotFound", "404");
                }

                // Update the user answer with the integer value
                // -2 = Strongly disagree, -1 = Disagree, 0 = Neutral, 1 = Agree, 2 = Strongly Agree
                await personalityAssessmentSet.update(
                    {
                        userAnswer: answer.user_answer,
                        userId: classInst.userId,
                        updatedAt: new Date()
                    },
                    { id: answer.question_assessment_set_id }
                );

                Logger.debug(`PersonalityAssessmentAnswer-updateAnswers: Updated answer for assessment set ${answer.question_assessment_set_id} with answer ${answer.user_answer} (${classInst.getAnswerDescription(answer.user_answer)})`);
            }
        } catch (error) {
            Logger.error(`PersonalityAssessmentAnswer-updateAnswers: Error updating answers. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("updateFailed", "500");
        }
    }

    private getAnswerDescription(userAnswer: number): string {
        switch (userAnswer) {
            case -2: return "Strongly disagree";
            case -1: return "Disagree";
            case 0: return "Neutral";
            case 1: return "Agree";
            case 2: return "Strongly Agree";
            default: return "Unknown";
        }
    }

    private prepareResponse(): Success {
        const classInst = this;
        return ResponseHelper.success({
            message: "All answers updated successfully",
            assessment_id: classInst.assessmentId,
            updated_answers_count: classInst.answers.length,
            answers: classInst.answers.map(answer => ({
                question_assessment_set_id: answer.question_assessment_set_id,
                user_answer: answer.user_answer,
                answer_description: classInst.getAnswerDescription(answer.user_answer)
            }))
        });
    }
}
