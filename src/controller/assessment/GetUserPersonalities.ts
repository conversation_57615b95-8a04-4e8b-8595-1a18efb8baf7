import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface RequestBody {
    user_id?: number; // Optional: get personalities for specific user (admin use)
    customParamsInReq: any;
}

interface PersonalityResult {
    id: number;
    name: string;
    rank: number; // 1 = top personality, 2 = second, 3 = third
}

interface AssessmentInfo {
    assessment_id: number;
    completed_at: Date;
    result_declared_at: Date;
    total_personalities: number;
}

export default class GetUserPersonalities extends BaseController {
    private targetUserId: number;
    private requestingUserId: number;
    private userAssessment: any;
    private assessmentResult: any;
    private personalityType: any;

    constructor(params: RequestBody) {
        super(params);
        const classInst = this;
        Logger.debug(`GetUserPersonalities-constructor: Param body: ${JSON.stringify(params)}`)
        Logger.debug(`GetUserPersonalities-constructor: User details: ${JSON.stringify(params.customParamsInReq?.currentUser)}`)

        classInst.requestingUserId = params.customParamsInReq?.currentUser?.userId;
        classInst.targetUserId = params.user_id || classInst.requestingUserId;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.initializeModels();
        await classInst.validateParams();
        const latestResult = await classInst.getLatestAssessmentResult();

        if (!latestResult) {
            return classInst.prepareNoResultResponse();
        }

        const personalities = await classInst.getPersonalityDetails(latestResult);
        const assessmentInfo = await classInst.getAssessmentInfo(latestResult);

        return classInst.prepareResponse(personalities, assessmentInfo);
    }

    private async initializeModels() {
        const classInst = this;
        const postgresModel = await PostgresModel.getDbModels();
        classInst.userAssessment = postgresModel.userAssessment;
        classInst.assessmentResult = postgresModel.assessmentResult;
        classInst.personalityType = postgresModel.personalityType;
    }

    private async validateParams() {
        const classInst = this;

        // Validate requesting user
        if (!classInst.requestingUserId) {
            return classInst.unauthorizedResponse("missingUserId", "400");
        }

        // Validate target user (if different from requesting user)
        if (classInst.targetUserId !== classInst.requestingUserId) {
            // For now, only allow users to get their own personalities
            // In future, this could be extended for admin users or public profiles
            return classInst.unauthorizedResponse("unauthorizedUserAccess", "403");
        }

        // Validate target user ID
        if (!classInst.targetUserId || typeof classInst.targetUserId !== 'number') {
            return classInst.unauthorizedResponse("invalidUserId", "400");
        }
    }

    private async getLatestAssessmentResult(): Promise<any> {
        const classInst = this;

        try {
            // Get the latest assessment result for the user
            const latestResult = await classInst.assessmentResult.getLatestAssessmentResult(classInst.targetUserId);

            if (!latestResult) {
                Logger.info(`GetUserPersonalities-getLatestAssessmentResult: No assessment results found for user ${classInst.targetUserId}`);
                return null;
            }

            Logger.debug(`GetUserPersonalities-getLatestAssessmentResult: Found latest result: ${JSON.stringify(latestResult)}`);
            return latestResult;
        } catch (error) {
            Logger.error(`GetUserPersonalities-getLatestAssessmentResult: Error getting latest assessment result. Exception: ${JSON.stringify(error)}`);
            return classInst.unauthorizedResponse("resultRetrievalFailed", "500");
        }
    }

    private async getPersonalityDetails(assessmentResult: any): Promise<PersonalityResult[]> {
        const classInst = this;

        try {
            const personalityTypeIds = assessmentResult.result || [];

            if (personalityTypeIds.length === 0) {
                Logger.warn(`GetUserPersonalities-getPersonalityDetails: No profession types found in result for user ${classInst.targetUserId}`);
                return [];
            }

            // Get profession type names
            const personalityTypeNames = await classInst.personalityType.getPersonalityTypeNames(personalityTypeIds);

            // Map to personality results with ranking
            const personalities: PersonalityResult[] = personalityTypeIds.map((id: number, index: number) => ({
                id: id,
                name: personalityTypeNames[id] || `Unknown Personality (ID: ${id})`,
                rank: index + 1
            }));

            Logger.debug(`GetUserPersonalities-getPersonalityDetails: Mapped ${personalities.length} personalities for user ${classInst.targetUserId}`);
            return personalities;
        } catch (error) {
            Logger.error(`GetUserPersonalities-getPersonalityDetails: Error getting personality details. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getAssessmentInfo(assessmentResult: any): Promise<AssessmentInfo> {
        const classInst = this;

        try {
            // Get assessment details
            const assessment = await classInst.userAssessment.findById(assessmentResult.assessment_id);

            if (!assessment) {
                Logger.warn(`GetUserPersonalities-getAssessmentInfo: Assessment not found for ID ${assessmentResult.assessment_id}`);
                return {
                    assessment_id: assessmentResult.assessment_id,
                    completed_at: new Date(),
                    result_declared_at: assessmentResult.created_at,
                    total_personalities: assessmentResult.result?.length || 0
                };
            }

            return {
                assessment_id: assessmentResult.assessment_id,
                completed_at: assessment.updatedAt,
                result_declared_at: assessmentResult.created_at,
                total_personalities: assessmentResult.result?.length || 0
            };
        } catch (error) {
            Logger.error(`GetUserPersonalities-getAssessmentInfo: Error getting assessment info. Exception: ${JSON.stringify(error)}`);
            // Return basic info even if detailed info fails
            return {
                assessment_id: assessmentResult.assessment_id,
                completed_at: new Date(),
                result_declared_at: assessmentResult.created_at,
                total_personalities: assessmentResult.result?.length || 0
            };
        }
    }

    private prepareNoResultResponse(): Success {
        const classInst = this;
        return ResponseHelper.success({
            message: "No assessment results found for user",
            user_id: classInst.targetUserId,
            has_results: false,
            personalities: [],
            assessment_info: null,
            suggestion: "Complete a personality assessment to see your results"
        });
    }

    private prepareResponse(personalities: PersonalityResult[], assessmentInfo: AssessmentInfo): Success {
        const classInst = this;

        // Separate personalities by rank for better presentation
        const topPersonality = personalities.find(p => p.rank === 1);
        const secondaryPersonalities = personalities.filter(p => p.rank > 1);

        return ResponseHelper.success({
            message: "User personalities retrieved successfully",
            user_id: classInst.targetUserId,
            has_results: true,
            personalities: {
                primary: topPersonality || null,
                secondary: secondaryPersonalities,
                all: personalities
            },
            assessment_info: {
                assessment_id: assessmentInfo.assessment_id,
                completed_at: assessmentInfo.completed_at,
                result_declared_at: assessmentInfo.result_declared_at,
                total_personalities: assessmentInfo.total_personalities
            },
            summary: {
                total_personalities: personalities.length,
                primary_personality: topPersonality?.name || "Not available",
                assessment_date: assessmentInfo.result_declared_at
            }
        });
    }
}
