import BaseController from "../BaseController";
import { Error, PersonalityAssessmentAPI, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import GenericOpHelpler from "../../lib/utils/GenericOpHelpler";
import Logger from "../../lib/Logger";
import {
    PersonalityAssessmentSetModelAttributes,
    UserAssessmentModelAttributes
} from "../../lib/types/Model";
import APINameConstant from "../../constant/APINameConstant";
import AssessmentConstants from "../../constant/AssessmentConstants";
import AssessmentRoutes from "../../routes/api/assessment/assessmentRoutes";


export default class PersonalityAssessment extends BaseController {
    private name: string;
    private password: string;
    private email: string;
    private username: string;
    private token: string;
    private dummyResponse: any;
    private userDetails: any;
    private assessmentId: number;
    private assessmentStatus: number;
    private phase: number;
    private responseData: PersonalityAssessmentAPI[];
    private responseDataQuestionMap: Record<number, PersonalityAssessmentAPI>

    constructor(params: any) {
        super(params);

        const classInst = this;
        classInst.userDetails = params.customParamsInReq.currentUser
        Logger.info(`User:: ${JSON.stringify(classInst.userDetails)}`)

        classInst.name = params.name
        classInst.password = params.password
        classInst.email = params.email
        classInst.username = params.username
        classInst.responseDataQuestionMap = {}
        classInst.phase = params.phase
        classInst.responseData = []

        classInst.dummyResponse = {
            assessment_id: 1,
            questions: [
                {
                    text: "Do you like working with on",
                    id: 1 // This ID belongs to the assessment configuration
                },
                {
                    text: "Would you prefer working",
                    id: 2
                },
                {
                    text: "Do you prefer working with",
                    id: 3
                },
                {
                    text: "Do you enjoy studying anatomy",
                    id: 4
                }
            ]
        }
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();

        /**
         * Check if user have any pending assessment
         * If pending assessment, return questions from previous assessment from where he left
         * Else, Get 50 question
         * Create new user assessment entry
         * Create personality assessment with question list -> Done
         * Assessment status:
         *      0 - Completed
         *      1 - Created
         *      2 - Started
         *      4 - Left
         *      5 - Timeout
         */
        await classInst.checkPendingAssessment();
        await classInst.createUserAssessment();
        await classInst.getQuestionsAndCreateAssessmentSet();

        return classInst.prepareResponse();
    }

    private async validateParams() {
        // Verify username is unique
        // Verify email is unique
        // Verify name is not empty
        // Password strength
    }

    private async checkPendingAssessment() {
        const classInst = this;
        const userAssessment = (await PostgresModel.getDbModels()).userAssessment;
        let userPendingAssessment = await userAssessment.getPendingAssessment();
        if (userPendingAssessment) {
            Logger.debug(`PersonalityAssessment-checkPendingAssessment: User pending assessment: ${JSON.stringify(userPendingAssessment)}`);
            classInst.assessmentId = userPendingAssessment.id
            classInst.assessmentStatus = userPendingAssessment.status
            Logger.info(`PersonalityAssessment-checkPendingAssessment: User have a pending assessment with id ${classInst.assessmentId} and status ${classInst.assessmentStatus}`)
        }
    }

    private async createUserAssessment() {
        const classInst = this;
        if (classInst.assessmentId) {
            Logger.info(`PersonalityAssessment-createUserAssessment: User already have a pending assessment. Returning...`);
            return;
        }
        const userAssessment = (await PostgresModel.getDbModels()).userAssessment;
        const userAssessmentDetails: UserAssessmentModelAttributes = {
            type: AssessmentConstants.personalityAssessmentStatus[AssessmentConstants.personalityAssessmentType],
            userId: classInst.userDetails.userId,
            status: AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated]
        }
        const createdAssessment: UserAssessmentModelAttributes | undefined = await userAssessment.create(userAssessmentDetails);

        if (!createdAssessment) {
            Logger.error(`PersonalityAssessment-createUserAssessment: Failed to create assessment. Response is undefined`);
            return classInst.unauthorizedResponse("assessmentCreationFailed", "500");
        }

        Logger.debug(`PersonalityAssessment-createUserAssessment: User assessment: ${JSON.stringify(createdAssessment)}: id: ${createdAssessment.id}`)
        classInst.assessmentId = createdAssessment.id!;
        classInst.assessmentStatus = createdAssessment.status
        Logger.info(`PersonalityAssessment-createUserAssessment: User assessment with id ${classInst.assessmentId} created with status ${classInst.assessmentStatus}`)
    }

    private async getQuestionsAndCreateAssessmentSet() {
        const classInst = this;
        if (AssessmentConstants.pendingAssessmentStatuses.includes(classInst.assessmentStatus)) {
            Logger.info(`PersonalityAssessment-getQuestionsAndCreateAssessmentSet: User already have a pending assessment. Status ${classInst.assessmentStatus}`);
            await classInst.getPendingAssessmentQuestions();
            if (classInst.responseData.length > 0) return;
        }
        const personalityQuestion = (await PostgresModel.getDbModels()).personalityQuestions;
        let questions: any[] = await personalityQuestion.get50QuestionGroupByProfession();
        let assessmentQuestionSet: PersonalityAssessmentSetModelAttributes[] = []
        for (let idx = 0; idx < questions.length; idx++) {
            let question = questions[idx];
            assessmentQuestionSet.push({
                assessmentId: classInst.assessmentId,
                questionId: question.id,
                userId: classInst.userDetails.userId
            })
            classInst.responseDataQuestionMap[question.id] = {
                questionId: question.id,
                text: question.text
            }
        }
        Logger.debug(`PersonalityAssessment-getQuestionsForAssessment: New assessment question set: ${JSON.stringify(assessmentQuestionSet)}`)
        Logger.debug(`PersonalityAssessment-getQuestionsForAssessment: New assessment question set length: ${assessmentQuestionSet.length}`)
        await classInst.createAssessmentQuestionsMap(assessmentQuestionSet)
    }

    private async getPendingAssessmentQuestions() {
        const classInst = this;
        const personalityAssessmentSetModel = (await PostgresModel.getDbModels()).personalityAssessmentSet;
        const personalityQuestion = (await PostgresModel.getDbModels()).personalityQuestions;

        // Get all questions for the pending assessment
        const assessmentQuestions = await personalityAssessmentSetModel.findByAssessmentId(classInst.assessmentId);

        Logger.debug(`PersonalityAssessment-getPendingAssessmentQuestions: Assessment questions: ${JSON.stringify(assessmentQuestions)}`);

        // Get question details for each assessment question
        for (const assessmentQuestion of assessmentQuestions) {
            const question = await personalityQuestion.findByPk(assessmentQuestion.questionId);
            if (question) {
                classInst.responseData.push({
                    text: question.text,
                    questionId: question.id!,
                    id: assessmentQuestion.id
                });
            }
        }
    }

    private async createAssessmentQuestionsMap(assessmentQuestionSet: any) {
        const classInst = this;
        /**
         * Get question for existing assessment
         */
        const personalityAssessmentSetModel = (await PostgresModel.getDbModels()).personalityAssessmentSet
        let recordByQuestionId: Record<number, {
            id: number
        }> | undefined = await personalityAssessmentSetModel.bulkCreate(assessmentQuestionSet);
        if (!recordByQuestionId) {
            return;
        }
        for (const [key, value] of Object.entries(classInst.responseDataQuestionMap)) {
            classInst.responseData.push({
                text: value.text,
                questionId: value.questionId,
                id: recordByQuestionId[Number(key)].id
            })
        }
    }

    private prepareResponse(): Success {
        const classInst = this;

        return ResponseHelper.success({
            redirectTo: "home",
            assessmentId: classInst.assessmentId,
            questions: classInst.responseData
        });
    }
}
