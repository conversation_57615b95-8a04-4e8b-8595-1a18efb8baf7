import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import GenericOpHelpler from "../../lib/utils/GenericOpHelpler";
import Logger from "../../lib/Logger";


export default class OccupationAssessmentAnswer extends BaseController {
    private name: string;
    private password: string;
    private email: string;
    private username: string;
    private token: string;

    constructor(params: any) {
        super(params);

        const classInst = this;
        classInst.name = params.name
        classInst.password = params.password
        classInst.email = params.email
        classInst.username = params.username
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;

        await classInst.validateParams();

        /**
         * Answers body should be list of object with following info
         *      - assessment_id
         *      - answers
         *          - occupation_assessment_id
         *          - user_choice
         */
        await classInst.updateAnswers();
        return classInst.prepareResponse();
    }

    private async validateParams() {
        // Verify username is unique
        // Verify email is unique
        // Verify name is not empty
        // Password strength
    }

    private async updateAnswers() {
        const classInst = this;
        /**
         * Update answers for test_id, question_id and answer.
         */
    }

    private prepareResponse(): Success {
        const classInst = this;

        return ResponseHelper.success({
            redirectTo: "home",
            data: {
                token: classInst.token,
                message: "Login successful"
            }
        });
    }
}
