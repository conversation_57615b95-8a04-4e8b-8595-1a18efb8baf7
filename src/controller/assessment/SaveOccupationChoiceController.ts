import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";

interface OccupationChoiceRequest {
    occupationIds: number[];
    customParamsInReq: any;
}

export default class SaveOccupationChoiceController extends BaseController {
    private occupationIds: number[];
    private userId: number;
    private userDetails: any;
    private dbModels: PostgresModel;

    constructor(params: OccupationChoiceRequest) {
        super(params);
        const classInst = this;
        Logger.debug(`SaveOccupationChoice-constructor: Params: ${JSON.stringify(params)}`);
        classInst.occupationIds = params.occupationIds || [];
        classInst.userDetails = params.customParamsInReq.currentUser;
        classInst.userId = classInst.userDetails.userId;
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;
        classInst.dbModels = await PostgresModel.getDbModels();

        try {
            await classInst.validateParams();
            await classInst.saveOccupationChoices();

            return classInst.prepareResponse();
        } catch (error: any) {
            Logger.error(`SaveOccupationChoice-servicePerform: Error saving occupation choices. Exception: ${JSON.stringify(error.message)}`);
            return ResponseHelper.error(['generalError'], {
                error: error.message
            });
        }
    }

    private async validateParams(): Promise<void> {
        const classInst = this;

        if (!classInst.userId) {
            throw new Error('User ID is required');
        }

        if (!Array.isArray(classInst.occupationIds) || classInst.occupationIds.length === 0) {
            throw new Error('At least one occupation ID is required');
        }

        // Validate that all occupation IDs exist
        const occupationModel = classInst.dbModels.occupation;
        const occupations = await occupationModel.getOccupationById(classInst.occupationIds);

        if (occupations.length !== classInst.occupationIds.length) {
            const foundIds = occupations.map(occ => occ.id);
            const invalidIds = classInst.occupationIds.filter(id => !foundIds.includes(id));
            throw new Error(`Invalid occupation IDs: ${invalidIds.join(', ')}`);
        }
    }

    private async saveOccupationChoices(): Promise<void> {
        const classInst = this;

        try {
            Logger.debug(`SaveOccupationChoice-saveOccupationChoices: Saving occupation choices for user ${classInst.userId}: ${JSON.stringify(classInst.occupationIds)}`);

            // Save or update the user's occupation choices
            const result = await classInst.dbModels.userOccupationChoice.updateOccupationList(
                classInst.userId,
                classInst.occupationIds
            );

            if (result) {
                Logger.info(`SaveOccupationChoice-saveOccupationChoices: Successfully saved occupation choices for user ${classInst.userId}`);
            } else {
                throw new Error('Failed to save occupation choices');
            }
        } catch (error: any) {
            Logger.error(`SaveOccupationChoice-saveOccupationChoices: Error saving occupation choices. Exception: ${JSON.stringify(error.message)}`);
            throw error;
        }
    }

    private prepareResponse(): Success {
        return ResponseHelper.success({
            message: "Occupation choices saved successfully",
            data: {
                userId: this.userId,
                occupationCount: this.occupationIds.length
            }
        });
    }
}
