import BaseController from "../BaseController";
import { Error, Success } from "../../lib/types/Response";
import ResponseHelper from "../../middlewares/ResponseHelper";
import PostgresModel from "../../db/PostgresModel";
import Logger from "../../lib/Logger";
import AssessmentConstants from "../../constant/AssessmentConstants";

interface AssessmentAnswer {
    question_assessment_set_id: number;
    user_answer: number;
}

export default class SavePersonalityAssessmentAnswer extends BaseController {
    private answers: AssessmentAnswer[];
    private userDetails: any;
    private assessmentId: number;

    constructor(params: any) {
        super(params);
        const classInst = this;
        Logger.debug(`SavePersonalityAssessmentAnswer-constructor: Param body: ${JSON.stringify(params)}`)
        classInst.userDetails = params.customParamsInReq.currentUser;
        classInst.answers = params;
        Logger.debug(`SavePersonalityAssessmentAnswer-constructor: User details: ${JSON.stringify(params)}`)
    }

    public async servicePerform(): Promise<Success | Error> {
        const classInst = this;
        await classInst.validateParams();
        await classInst.updateAssessmentAnswers();
        await classInst.updateAssessmentStatus();
        return classInst.prepareResponse();
    }

    private async validateParams() {
        const classInst = this;
        // if (!classInst.answers || !Array.isArray(classInst.answers) || classInst.answers.length === 0) {
        //     return classInst.unauthorizedResponse('invalidAnswer', 's_l_v_ap_spqa_0');
        // }

        // for (const answer of classInst.answers) {
        //     if (!answer.id || (answer.user_answer !== 0 && answer.user_answer !== 1)) {
        //         return classInst.unauthorizedResponse('invalidAnswer', 's_l_v_ap_spqa_1');
        //     }
        // }
    }

    private async updateAssessmentAnswers() {
        const classInst = this;
        const personalityAssessmentSet = (await PostgresModel.getDbModels()).personalityAssessmentSet;
        
        // Get assessment ID from the first answer
        const firstAnswer = await personalityAssessmentSet.findById(classInst.answers[0].question_assessment_set_id);
        if (!firstAnswer) {
            throw new Error(`Invalid personality assessment set ID ${classInst.answers[0].question_assessment_set_id}`);
        }
        classInst.assessmentId = firstAnswer.assessmentId;

        // Update all answers
        for (const answer of classInst.answers) {
            await personalityAssessmentSet.update(
                { userAnswer: answer.user_answer },
                { id: answer.question_assessment_set_id } 
            );
        }
    }

    private async updateAssessmentStatus() {
        const classInst = this;
        const userAssessment = (await PostgresModel.getDbModels()).userAssessment;
        
        // Update assessment status to started if it's in created state
        await userAssessment.update(
            { status: AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusStarted] },
            { 
                id: classInst.assessmentId,
                status: AssessmentConstants.assessmentStatus[AssessmentConstants.assessmentStatusCreated]
            } 
        );
    }

    private prepareResponse(): Success {
        return ResponseHelper.success({
            message: "Assessment answers saved successfully",
            data: {
                assessmentId: this.assessmentId
            }
        });
    }
} 
