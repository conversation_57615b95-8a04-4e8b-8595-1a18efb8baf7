import express, { Request, Response } from 'express';
import bodyParser from "body-parser";
import { authenticateToken } from "../../../middlewares/AuthMiddleware";
import APINameConstant from "../../../constant/APINameConstant";
import { Success } from "../../../lib/types/Response";
import ResponseHelper from "../../../middlewares/ResponseHelper";
import Signup from "../../../controller/user/Signup";
import Logger from "../../../lib/Logger";
import Login from "../../../controller/user/Login";
import RefreshToken from "../../../controller/user/RefreshToken";
import Logout from "../../../controller/user/Logout";
import UpdateUserProfile from "../../../controller/user/UpdateUserProfile";
import SignupWithLink from "../../../controller/user/SignupWithLink";
import VerifyOtp from "../../../controller/user/VerifyOtp";
import ResendOtp from "../../../controller/user/ResendOtp";

const router = express();

router.use(bodyParser.json());

router.get('/get-users', authenticateToken, (req: Request, res: Response) => {
    res.json({ message: `Hello ${(req as any).user.username}, welcome to the protected route!` });
});

router.post('/login', async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.userLogin;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new Login(requestBody).perform();
    Logger.debug(`userRoutes-Login:serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.post('/refresh-token', async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.refreshToken;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new RefreshToken(requestBody).perform();
    Logger.debug(`userRoutes-RefreshToken:serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.post('/logout', authenticateToken, async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.logout;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new Logout(requestBody).perform();
    Logger.debug(`userRoutes-Logout:serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.post('/signup', async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.userSignup;
    Logger.debug(`userRoutes-Signup: Request body: ${JSON.stringify(req.body)}`);
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new Signup(requestBody).perform();
    Logger.debug(`userRoutes-Signup: serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.put('/update-profile', authenticateToken, async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.updateUserProfile;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new UpdateUserProfile(requestBody).perform();
    Logger.debug(`userRoutes-UpdateUserProfile: serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.post('/signup-with-link', async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.signupWithLink;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new SignupWithLink(requestBody).perform();
    Logger.debug(`userRoutes-SignupWithLink: serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.post('/verify-otp', async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.verifyOtp;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new VerifyOtp(requestBody).perform();
    Logger.debug(`userRoutes-VerifyOtp: serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

router.post('/resend-otp', async (req: Request, res: Response) => {
    req.customParamsInReq.apiName = APINameConstant.resendOtp;
    let requestBody = req.body;
    Object.assign(requestBody, { customParamsInReq: req.customParamsInReq });
    let serviceResponse = await new ResendOtp(requestBody).perform();
    Logger.debug(`userRoutes-ResendOtp: serviceResponse: ${JSON.stringify(serviceResponse)}`);
    ResponseHelper.renderWithStatus(serviceResponse, res);
});

export default router;
