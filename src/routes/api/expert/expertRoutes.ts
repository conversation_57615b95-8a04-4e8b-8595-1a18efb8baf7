import { response, Router } from 'express';
import OccupationListController from '../../../controller/OccupationListController';
import PostgresModel from '../../../db/PostgresModel';
import { authenticateToken } from '../../../middlewares/AuthMiddleware';
import ResponseHelper from '../../../middlewares/ResponseHelper';
import APINameConstant from '../../../constant/APINameConstant';

const router = Router();



export default router;
