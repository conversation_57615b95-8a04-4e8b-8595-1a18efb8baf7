'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface: QueryInterface, sequelize: typeof DataTypes) {
		await queryInterface.createTable('user_occupation_choice', {
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
			},
			user_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: { tableName: 'users' },
					key: 'id'
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE'
			},
			occupation_list: {
				type: DataTypes.ARRAY(DataTypes.INTEGER),
				allowNull: false,
				defaultValue: []
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
			}
		});

		// Add index on user_id for faster lookups
		await queryInterface.addIndex('user_occupation_choice', {
			fields: ['user_id'],
			unique: true
		});
	},

	async down(queryInterface: QueryInterface, sequelize: typeof DataTypes) {
		await queryInterface.dropTable('user_occupation_choice');
	}
};
