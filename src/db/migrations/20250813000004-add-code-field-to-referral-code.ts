'use strict';
import { QueryInterface, DataTypes } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        // Add the code field to store the actual referral code string
        await queryInterface.addColumn('referral_code', 'code', {
            type: sequelize.STRING(20),
            allowNull: false,
            unique: true
        });

        // Add index on code for faster lookups
        await queryInterface.addIndex('referral_code', {
            fields: ['code'],
            unique: true
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.removeColumn('referral_code', 'code');
    }
};
