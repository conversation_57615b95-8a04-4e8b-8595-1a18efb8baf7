'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('personality_assessment_set', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            assessment_id: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'user_assessments' },
                    key: 'id'
                },
                allowNull: false
            },
            question_id: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'personality_questions' },
                    key: 'id'
                },
                allowNull: false
            },
            user_id: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                allowNull: false
            },
            user_answer: {
                type: sequelize.SMALLINT,
                allowNull: true
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('personality_assessment_set');
    }
}
