'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('schools', {
                id: {
                    type: sequelize.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                name: {
                    type: sequelize.STRING(100),
                    allowNull: true,
                },
                email: {
                    type: sequelize.STRING(100),
                    allowNull: false,
                    unique: true,
                },
                code: {
                    type: sequelize.STRING(15),
                    unique: true,
                },
                mobile_number: {
                    type: sequelize.BIGINT,
                    unique: true,
                    allowNull: false,
                },
                verified_email: {
                    type: sequelize.BOOLEAN,
                    allowNull: false,
                    defaultValue: false,
                },
                verified_number: {
                    type: sequelize.BOOLEAN,
                    allowNull: false,
                    defaultValue: false,
                },
                created_at: {
                    type: sequelize.DATE,
                    allowNull: false,
                    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
                },
                updated_at: {
                    type: sequelize.DATE,
                    allowNull: false,
                    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
                },
            });

        await queryInterface.addIndex('schools', {
            unique: true,
            fields: ["email"]
        });

        await queryInterface.addIndex('schools', {
            unique: true,
            fields: ["code"]
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('schools');
    }
}
