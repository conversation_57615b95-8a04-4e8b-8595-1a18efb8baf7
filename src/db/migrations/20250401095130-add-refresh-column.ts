'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface: QueryInterface, sequelize: typeof DataTypes) {
    await queryInterface.addColumn('users', 'refresh_token', {
      type: DataTypes.STRING,
      allowNull: true
    });
  },

  async down (queryInterface: QueryInterface, sequelize: typeof DataTypes) {
    await queryInterface.removeColumn('users', 'refresh_token');
  }
};
