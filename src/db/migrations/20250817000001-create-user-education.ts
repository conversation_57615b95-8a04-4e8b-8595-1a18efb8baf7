'use strict';

import { DataTypes, QueryInterface, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('user_education', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            user_id: {
                type: sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
                unique: true // One education record per user
            },
            // High School Details
            high_school_stream_id: {
                type: sequelize.INTEGER,
                allowNull: true,
                references: {
                    model: { tableName: 'education_streams' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'SET NULL'
            },
            tenth_percentage: {
                type: sequelize.DECIMAL(5, 2), // Allows values like 99.99
                allowNull: true,
                validate: {
                    min: 0,
                    max: 100
                }
            },
            eleventh_percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
                validate: {
                    min: 0,
                    max: 100
                }
            },
            twelfth_percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
                validate: {
                    min: 0,
                    max: 100
                }
            },
            // Future extensibility for graduation details
            graduation_degree: {
                type: sequelize.STRING(100),
                allowNull: true
            },
            graduation_specialization: {
                type: sequelize.STRING(100),
                allowNull: true
            },
            graduation_college: {
                type: sequelize.STRING(200),
                allowNull: true
            },
            graduation_percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
                validate: {
                    min: 0,
                    max: 100
                }
            },
            graduation_start_year: {
                type: sequelize.INTEGER,
                allowNull: true
            },
            graduation_end_year: {
                type: sequelize.INTEGER,
                allowNull: true
            },
            // Post graduation details
            post_graduation_degree: {
                type: sequelize.STRING(100),
                allowNull: true
            },
            post_graduation_specialization: {
                type: sequelize.STRING(100),
                allowNull: true
            },
            post_graduation_college: {
                type: sequelize.STRING(200),
                allowNull: true
            },
            post_graduation_percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
                validate: {
                    min: 0,
                    max: 100
                }
            },
            post_graduation_start_year: {
                type: sequelize.INTEGER,
                allowNull: true
            },
            post_graduation_end_year: {
                type: sequelize.INTEGER,
                allowNull: true
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });

        // Add indexes for better query performance
        await queryInterface.addIndex('user_education', {
            fields: ["user_id"]
        });

        await queryInterface.addIndex('user_education', {
            fields: ["high_school_stream_id"]
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('user_education');
    }
}
