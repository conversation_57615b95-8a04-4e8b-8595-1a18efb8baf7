'use strict';

import { DataTypes, QueryInterface, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        // Drop the existing table if it exists
        await queryInterface.dropTable('user_education');

        // Create the new user_education table with one-to-many relationship
        await queryInterface.createTable('user_education', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            user_id: {
                type: sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE'
            },
            education_type: {
                type: sequelize.INTEGER,
                allowNull: false,
                comment: 'Type of education: 1=high_school, 2=graduation, 3=post_graduation, 4=diploma, 5=certification, etc.'
            },
            // Institution details
            institution_name: {
                type: sequelize.STRING(200),
                allowNull: true,
                comment: 'Name of school/college/university/institution'
            },
            // Degree/Course details
            degree_name: {
                type: sequelize.STRING(100),
                allowNull: true,
                comment: 'Name of degree/course (e.g., B.Tech, M.Tech, 12th, Certification in AWS)'
            },
            specialization: {
                type: sequelize.STRING(100),
                allowNull: true,
                comment: 'Specialization/Stream (e.g., Computer Science, Science, Commerce)'
            },
            // For high school - reference to education_streams table
            education_stream_id: {
                type: sequelize.INTEGER,
                allowNull: true,
                references: {
                    model: { tableName: 'education_streams' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'SET NULL',
                comment: 'Reference to education_streams table for high school stream'
            },
            // Academic performance
            percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
                validate: {
                    min: 0,
                    max: 100
                },
                comment: 'Academic percentage/marks obtained'
            },
            grade: {
                type: sequelize.STRING(10),
                allowNull: true,
                comment: 'Grade obtained (e.g., A+, B, First Class)'
            },
            // Duration
            start_year: {
                type: sequelize.INTEGER,
                allowNull: true,
                comment: 'Year when education started'
            },
            end_year: {
                type: sequelize.INTEGER,
                allowNull: true,
                comment: 'Year when education completed'
            },
            // Status
            is_completed: {
                type: sequelize.BOOLEAN,
                allowNull: false,
                defaultValue: true,
                comment: 'Whether the education is completed or ongoing'
            },
            // Additional details
            board_university: {
                type: sequelize.STRING(100),
                allowNull: true,
                comment: 'Board/University name (e.g., CBSE, ICSE, Delhi University)'
            },
            location: {
                type: sequelize.STRING(100),
                allowNull: true,
                comment: 'Location of institution (city, state)'
            },
            // For certifications
            certification_authority: {
                type: sequelize.STRING(100),
                allowNull: true,
                comment: 'Authority that issued the certification (e.g., AWS, Google, Microsoft)'
            },
            certificate_number: {
                type: sequelize.STRING(50),
                allowNull: true,
                comment: 'Certificate number or ID'
            },
            expiry_date: {
                type: sequelize.DATE,
                allowNull: true,
                comment: 'Expiry date for certifications'
            },
            // Metadata
            notes: {
                type: sequelize.TEXT,
                allowNull: true,
                comment: 'Additional notes or details'
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });

        // Add indexes for better query performance
        await queryInterface.addIndex('user_education', {
            fields: ["user_id"]
        });

        await queryInterface.addIndex('user_education', {
            fields: ["education_type"]
        });

        await queryInterface.addIndex('user_education', {
            fields: ["user_id", "education_type"]
        });

        await queryInterface.addIndex('user_education', {
            fields: ["education_stream_id"]
        });

        // Add unique constraint for high school records (one high school record per user)
        await queryInterface.addIndex('user_education', {
            unique: true,
            fields: ["user_id", "education_type"],
            where: {
                education_type: 1 // HIGH_SCHOOL type
            },
            name: 'unique_user_high_school'
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('user_education');
        
        // Recreate the old table structure if needed
        await queryInterface.createTable('user_education', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            user_id: {
                type: sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
                unique: true
            },
            high_school_stream_id: {
                type: sequelize.INTEGER,
                allowNull: true,
                references: {
                    model: { tableName: 'education_streams' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'SET NULL'
            },
            tenth_percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
            },
            eleventh_percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
            },
            twelfth_percentage: {
                type: sequelize.DECIMAL(5, 2),
                allowNull: true,
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });
    }
}
