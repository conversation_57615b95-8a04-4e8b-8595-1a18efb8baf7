'use strict';

import { QueryInterface, DataTypes } from 'sequelize';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface: QueryInterface) => {
		await queryInterface.createTable('college_tier', {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER
			},
			occupation_type: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'expert_profile',
					key: 'id'
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE'
			},
			tier: {
				type: DataTypes.SMALLINT,
				allowNull: false
			},
			start_salary: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			end_salary: {
				type: DataTypes.INTEGER,
				allowNull: false
			},
			created_at: {
				allowNull: false,
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW
			},
			updated_at: {
				allowNull: false,
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW
			}
		});
	},

	down: async (queryInterface: QueryInterface) => {
		await queryInterface.dropTable('college_tier');
	}
};
