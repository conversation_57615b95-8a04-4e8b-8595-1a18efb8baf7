'use strict';
import { QueryInterface, DataTypes } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.addColumn('users', 'referral_code_id', {
            type: sequelize.INTEGER,
            allowNull: true,
            references: {
                model: { tableName: 'referral_code' },
                key: 'id'
            },
            onUpdate: 'CASCADE',
            onDelete: 'SET NULL'
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.removeColumn('users', 'referral_code_id');
    }
};
