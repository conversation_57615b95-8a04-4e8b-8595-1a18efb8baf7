'use strict';

import { DataTypes, QueryInterface, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        // First, drop the unique constraint on user_id to allow multiple records per user
        await queryInterface.removeConstraint('user_education', 'user_education_user_id_key');
        
        // Add education_type field to distinguish between different types of education
        await queryInterface.addColumn('user_education', 'education_type', {
            type: sequelize.ENUM('HIGH_SCHOOL', 'GRADUATION', 'POST_GRADUATION', 'CERTIFICATION', 'DIPLOMA', 'OTHER'),
            allowNull: false,
            defaultValue: 'HIGH_SCHOOL'
        });

        // Add institution_name field for school/college/university name
        await queryInterface.addColumn('user_education', 'institution_name', {
            type: sequelize.STRING(200),
            allowNull: true
        });

        // Add degree_name field (more flexible than separate graduation_degree field)
        await queryInterface.addColumn('user_education', 'degree_name', {
            type: sequelize.STRING(100),
            allowNull: true
        });

        // Add specialization field (more flexible than separate graduation_specialization field)
        await queryInterface.addColumn('user_education', 'specialization', {
            type: sequelize.STRING(100),
            allowNull: true
        });

        // Add percentage/grade field (unified field for all education types)
        await queryInterface.addColumn('user_education', 'percentage_or_grade', {
            type: sequelize.STRING(20), // Can store "85.5%" or "A+" or "8.5 CGPA"
            allowNull: true
        });

        // Add start_year and end_year for all education types
        await queryInterface.addColumn('user_education', 'start_year', {
            type: sequelize.INTEGER,
            allowNull: true
        });

        await queryInterface.addColumn('user_education', 'end_year', {
            type: sequelize.INTEGER,
            allowNull: true
        });

        // Add board_or_university field
        await queryInterface.addColumn('user_education', 'board_or_university', {
            type: sequelize.STRING(200),
            allowNull: true
        });

        // Add is_current field to indicate if education is ongoing
        await queryInterface.addColumn('user_education', 'is_current', {
            type: sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false
        });

        // Remove old specific fields that are now redundant
        await queryInterface.removeColumn('user_education', 'tenth_percentage');
        await queryInterface.removeColumn('user_education', 'eleventh_percentage');
        await queryInterface.removeColumn('user_education', 'twelfth_percentage');
        await queryInterface.removeColumn('user_education', 'graduation_degree');
        await queryInterface.removeColumn('user_education', 'graduation_specialization');
        await queryInterface.removeColumn('user_education', 'graduation_college');
        await queryInterface.removeColumn('user_education', 'graduation_percentage');
        await queryInterface.removeColumn('user_education', 'graduation_start_year');
        await queryInterface.removeColumn('user_education', 'graduation_end_year');
        await queryInterface.removeColumn('user_education', 'post_graduation_degree');
        await queryInterface.removeColumn('user_education', 'post_graduation_specialization');
        await queryInterface.removeColumn('user_education', 'post_graduation_college');
        await queryInterface.removeColumn('user_education', 'post_graduation_percentage');
        await queryInterface.removeColumn('user_education', 'post_graduation_start_year');
        await queryInterface.removeColumn('user_education', 'post_graduation_end_year');

        // Add new indexes for better query performance
        await queryInterface.addIndex('user_education', {
            fields: ["user_id", "education_type"]
        });

        await queryInterface.addIndex('user_education', {
            fields: ["education_type"]
        });

        await queryInterface.addIndex('user_education', {
            fields: ["user_id", "is_current"]
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        // Remove new columns
        await queryInterface.removeColumn('user_education', 'education_type');
        await queryInterface.removeColumn('user_education', 'institution_name');
        await queryInterface.removeColumn('user_education', 'degree_name');
        await queryInterface.removeColumn('user_education', 'specialization');
        await queryInterface.removeColumn('user_education', 'percentage_or_grade');
        await queryInterface.removeColumn('user_education', 'start_year');
        await queryInterface.removeColumn('user_education', 'end_year');
        await queryInterface.removeColumn('user_education', 'board_or_university');
        await queryInterface.removeColumn('user_education', 'is_current');

        // Add back old columns
        await queryInterface.addColumn('user_education', 'tenth_percentage', {
            type: sequelize.DECIMAL(5, 2),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'eleventh_percentage', {
            type: sequelize.DECIMAL(5, 2),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'twelfth_percentage', {
            type: sequelize.DECIMAL(5, 2),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'graduation_degree', {
            type: sequelize.STRING(100),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'graduation_specialization', {
            type: sequelize.STRING(100),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'graduation_college', {
            type: sequelize.STRING(200),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'graduation_percentage', {
            type: sequelize.DECIMAL(5, 2),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'graduation_start_year', {
            type: sequelize.INTEGER,
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'graduation_end_year', {
            type: sequelize.INTEGER,
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'post_graduation_degree', {
            type: sequelize.STRING(100),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'post_graduation_specialization', {
            type: sequelize.STRING(100),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'post_graduation_college', {
            type: sequelize.STRING(200),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'post_graduation_percentage', {
            type: sequelize.DECIMAL(5, 2),
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'post_graduation_start_year', {
            type: sequelize.INTEGER,
            allowNull: true
        });
        await queryInterface.addColumn('user_education', 'post_graduation_end_year', {
            type: sequelize.INTEGER,
            allowNull: true
        });

        // Add back unique constraint
        await queryInterface.addConstraint('user_education', {
            fields: ['user_id'],
            type: 'unique',
            name: 'user_education_user_id_key'
        });
    }
}
