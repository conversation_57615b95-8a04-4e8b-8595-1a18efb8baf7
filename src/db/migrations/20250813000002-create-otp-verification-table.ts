'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('otp_verifications', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            user_id: {
                type: sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE'
            },
            otp_code: {
                type: sequelize.STRING(6),
                allowNull: false,
            },
            otp_type: {
                type: sequelize.INTEGER,
                allowNull: false,
                defaultValue: 1, // 1 = email_verification, 2 = password_reset
                comment: '1=email_verification, 2=password_reset'
            },
            expires_at: {
                type: sequelize.DATE,
                allowNull: false,
            },
            is_used: {
                type: sequelize.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });

        // Add indexes for efficient queries
        await queryInterface.addIndex('otp_verifications', {
            fields: ['user_id', 'otp_type']
        });

        await queryInterface.addIndex('otp_verifications', {
            fields: ['expires_at']
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('otp_verifications');
    }
};
