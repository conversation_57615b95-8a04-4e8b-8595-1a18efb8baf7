'use strict';

import { DataTypes, QueryInterface, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('users', {
                id: {
                    type: sequelize.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                name: {
                    type: sequelize.STRING(100),
                    allowNull: true,
                },
                email: {
                    type: sequelize.STRING(100),
                    allowNull: true,
                    unique: true,
                },
                password: {
                    type: sequelize.STRING(100),
                    allowNull: true
                },
                username: {
                    type: sequelize.STRING(15),
                    unique: true,
                },
                mobile_number: {
                    type: sequelize.STRING(13),
                    unique: true,
                    allowNull: true,
                },
                verified_email: {
                    type: sequelize.BOOLEAN,
                    allowNull: false,
                    defaultValue: false,
                },
                verified_number: {
                    type: sequelize.BOOLEAN,
                    allowNull: false,
                    defaultValue: false,
                },
                created_at: {
                    type: sequelize.DATE,
                    allowNull: false,
                    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
                },
                updated_at: {
                    type: sequelize.DATE,
                    allowNull: false,
                    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
                },
            });

        await queryInterface.addIndex('users', {
            unique: true,
            fields: ["email"]
        });

        await queryInterface.addIndex('users', {
            unique: true,
            fields: ["username"]
        });
    },

    down: async (queryInterface: any, sequelize: any) => {
        await queryInterface.dropTable('users');
    }
}
