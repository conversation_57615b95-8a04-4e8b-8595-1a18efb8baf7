'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('user_assessments', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            type: {
                type: sequelize.SMALLINT,
                allowNull: true,
            },
            user_id: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                allowNull: false
            },
            status: {
                type: sequelize.SMALLINT,
                allowNull: true,
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('user_assessments');
    }
}
