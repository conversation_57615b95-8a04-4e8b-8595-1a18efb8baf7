'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('education_streams', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: sequelize.STRING(50),
                allowNull: false,
            },
            skill_level: {
                type: sequelize.SMALLINT,
                allowNull: false,
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('education_streams');
    }
}
