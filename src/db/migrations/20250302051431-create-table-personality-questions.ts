'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('personality_questions', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            text: {
                type: sequelize.STRING(100),
                allowNull: true,
            },
            personality_type: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'personality_types' },
                    key: 'id'
                },
                allowNull: false
            },
            interest_tags: {
                type: sequelize.ARRAY(sequelize.INTEGER),
                references: {
                    model: { tableName: 'interest_tags' },
                    key: 'id'
                },
                allowNull: false,
                defaultValue: [],
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('personality_questions');
    }
}
