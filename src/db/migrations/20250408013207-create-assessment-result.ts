'use strict'
import { QueryInterface, DataTypes } from 'sequelize';

export default {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('assessment_result', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true,
                allowNull: false,
            },
            assessment_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'user_assessments',
                    key: 'id',
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id',
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
            },
            result: {
                type: sequelize.ARRAY(sequelize.INTEGER),
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
        });

        // Add indexes for better query performance
        await queryInterface.addIndex('assessment_result', ['assessment_id']);
        await queryInterface.addIndex('assessment_result', ['user_id']);
    },

    down: async (queryInterface: QueryInterface) => {
        await queryInterface.dropTable('assessment_result');
    },
};
