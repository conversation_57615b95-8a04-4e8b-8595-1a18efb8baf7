'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('referral_code', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            code: {
                type: sequelize.STRING(20),
                allowNull: false,
                unique: true,
            },
            valid_till: {
                type: sequelize.DATE,
                allowNull: false,
            },
            associated_school_id: {
                type: sequelize.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'schools' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE'
            },
            usages_count: {
                type: sequelize.INTEGER,
                allowNull: false,
                defaultValue: 0,
            },
            extra_validations: {
                type: sequelize.JSON,
                allowNull: true,
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });

        // Add index on associated_school_id for faster lookups
        await queryInterface.addIndex('referral_code', {
            fields: ['associated_school_id']
        });

        // Add index on valid_till for efficient date-based queries
        await queryInterface.addIndex('referral_code', {
            fields: ['valid_till']
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('referral_code');
    }
};
