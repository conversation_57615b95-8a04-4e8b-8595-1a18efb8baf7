'use strict';
import { QueryInterface, DataTypes, Sequelize } from "sequelize";

module.exports = {
    up: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.createTable('occupation_assessment_set', {
            id: {
                type: sequelize.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            assessment_id: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'user_assessments' },
                    key: 'id'
                },
                allowNull: false
            },
            occupation_option1: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'occupations' },
                    key: 'id'
                },
                allowNull: false
            },
            occupation_option2: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'occupations' },
                    key: 'id'
                },
                allowNull: false
            },
            user_id: {
                type: sequelize.INTEGER,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                allowNull: false
            },
            user_choice: {
                type: sequelize.SMALLINT,
                allowNull: true
            },
            created_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: sequelize.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        });
    },

    down: async (queryInterface: QueryInterface, sequelize: typeof DataTypes) => {
        await queryInterface.dropTable('occupation_assessment_set');
    }
}
