const SequelizeConfig: { [key: string]: any } = {
    development: {
        username: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: process.env.DATABASE_NAME,
        host: process.env.DATABASE_HOST,
        port: Number(process.env.DATABASE_PORT),
        dialect: "postgres",
        seederStorage: "sequelize",
        seederStorageTableName: "SequelizeMetaSeeder",
        models: '../../models',
        logging: true
    },
    staging: {
        username: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: process.env.DATABASE_DEFAULT_DB,
        host: process.env.DATABASE_HOST,
        port: Number(process.env.DATABASE_PORT),
        dialect: "postgres",
        // dialectOptions: {
        //     ssl: {
        //         rejectUnauthorized: true,
        //         ca: (() => {
        //             return GeneralConstant.caCertPermissionFilePath ? fs.readFileSync(GeneralConstant.caCertPermissionFilePath) : null;
        //         })(
        // ERROR: missing ) after argument list)
        //     }
        // },
        seederStorage: "sequelize",
        seederStorageTableName: "SequelizeMetaSeeder",
        models: '../../models',
        logging: true
    },
    production: {
        username: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: process.env.DATABASE_DEFAULT_DB,
        host: process.env.DATABASE_HOST,
        port: Number(process.env.DATABASE_PORT),
        dialect: "postgres",
        // dialectOptions: {
        //     ssl: {
        //         rejectUnauthorized: true,
        //         ca: (() => {
        //             return GeneralConstant.caCertPermissionFilePath ? fs.readFileSync(GeneralConstant.caCertPermissionFilePath) : null;
        //         })()
        //     }
        // },
        seederStorage: "sequelize",
        seederStorageTableName: "SequelizeMetaSeeder",
        models: '../../models',
        logging: true
    }
};

export = SequelizeConfig;
