import { Sequelize } from "sequelize";
import Logger from "../lib/Logger"
import SequelizeConfig from "./config/config";

class Database {
    private static instance: Sequelize;

    public static getInstance(): Sequelize {
        if (!Database.instance) {
            Database.instance = new Sequelize(SequelizeConfig[process.env.ENVIRONMENT!]);
        }
        return Database.instance;
    }

    public static async connect() {
        try {
            await Database.getInstance().authenticate();
            Logger.info("✅ PostgreSQL connected successfully!");
        } catch (error) {
            Logger.error("❌ Error connecting to PostgreSQL:", error);
            process.exit(1);
        }
    }

    public static async disconnect() {
        try {
            await Database.getInstance().close();
            Logger.info("✅ Database connection closed.");
        } catch (error) {
            Logger.error("❌ Error closing database connection:", error);
        }
    }
}

export default Database;
