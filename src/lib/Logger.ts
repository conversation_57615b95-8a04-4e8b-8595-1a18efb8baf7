import winston from 'winston';

const { LOG_LEVEL } = process.env;
export default winston.createLogger({
    level: LOG_LEVEL || 'info',
    format: winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp(),
        winston.format.simple(),
    ),
    defaultMeta: { service: 'st-backend' },
    transports: [new winston.transports.Console()],
    exceptionHandlers: [new winston.transports.Console()],
});
