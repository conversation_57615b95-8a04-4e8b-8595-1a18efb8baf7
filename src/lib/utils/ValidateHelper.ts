import BigNumber from 'bignumber.js';

const he = require('he');

/**
 * Class for common validators.
 *
 * @class CommonValidator
 */
class ValidateHelper {
    [x: string]: any;

    /**
     * Is valid Boolean?
     *
     * @returns {boolean}
     */
    validateBoolean(str: string | boolean): boolean {
        const oThis = this;

        if (oThis.isVarNullOrUndefined(str)) {
            return false;
        }

        return str === 'true' || str === 'false' || str === true || str === false;
    }

    /**
     * Is var null or undefined?
     *
     * @param {object/string/integer/boolean} variable
     *
     * @returns {boolean}
     */
    isVarNullOrUndefined(variable: any): boolean {
        return typeof variable === 'undefined' || variable == null;
    }

    /**
     * Is var null?
     *
     * @param variable
     *
     * @returns {boolean}
     */
    isVarNull(variable: string): boolean {
        return variable == null;
    }

    /**
     * Is var undefined?
     *
     * @param variable
     *
     * @returns {boolean}
     */
    isVarUndefined(variable: object): boolean {
        return typeof variable === 'undefined';
    }

    /**
     * Is var not blank or null?
     *
     * @param {array<string>} variable
     *
     * @returns {boolean}
     */
    validateNonBlankString(variable: string): boolean {
        const oThis = this;
        return oThis.validateNonBlankStringArray([variable]);
    }

    /**
     * Is var not blank or null
     *
     * @param {array<string>} array
     *
     * @returns {boolean}
     */
    validateNonBlankStringArray(array: string[]): boolean {
        const oThis = this;

        if (Array.isArray(array)) {
            for (let index = 0; index < array.length; index++) {
                const variable = array[index];
                if (
                    oThis.isVarNullOrUndefined(variable)
                    || !oThis.validateString(variable)
                    || variable == ''
                ) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    /**
     * Is var true?
     *
     * @returns {boolean}
     */
    isVarTrue(variable: string | boolean): boolean {
        return variable === true || variable === 'true';
    }

    /**
     * Is var false?
     *
     * @returns {boolean}
     */
    isVarFalse(variable: string | boolean): boolean {
        return variable === false || variable === 'false';
    }

    /**
     * Is var integer?
     *
     * @returns {boolean}
     */
    validateInteger(variable: number): boolean {
        try {
            const variableInBn = new BigNumber(String(variable));
            // Variable is integer and its length is less than 37 digits
            if (variableInBn.isInteger() && variableInBn.toString(10).length <= 37) {
                return true;
            }
        } catch (e: any) {
        }

        return false;
    }

    /**
     * Is integer non zero?
     *
     * @param {string/number} variable
     *
     * @returns {boolean}
     */
    validateNonZeroInteger(variable: number): boolean {
        const oThis = this;

        if (oThis.validateInteger(variable)) {
            return Number(variable) > 0;
        }

        return false;
    }

    /**
     * Is integer negative?
     *
     * @param {string/number} variable
     *
     * @returns {boolean}
     */
    validateNegativeInteger(variable: number): boolean {
        const oThis = this;

        if (oThis.validateInteger(variable)) {
            return Number(variable) < 0;
        }

        return false;
    }

    /**
     * Is string valid ?
     *
     * @returns {boolean}
     */
    validateString(variable: string): boolean {
        return typeof variable === 'string';
    }

    /**
     * Checks if the given string starts with 0x.
     *
     * @param {string} variable
     *
     * @returns {boolean}
     */
    validateHexString(variable: string): boolean {
        return /^0x[a-z0-9]{1,}$/i.test(variable);
    }

    /**
     * Is var a string containing only alphabets?
     *
     * @param {string} variable
     *
     * @returns {boolean}
     */
    validateAlphaString(variable: string): boolean {
        const oThis = this;
        if (oThis.isVarNullOrUndefined(variable)) {
            return false;
        }

        return /^[a-z]+$/i.test(variable);
    }

    /**
     * Validate allowed characters in channel name.
     *
     * @param {string} string
     *
     * @returns {boolean}
     */
    validateUserNameAllowedCharacters(string: string): boolean {
        const oThis = this;
        if (oThis.validateNonEmptyString(string)) {
            const trimmedString = string.trim();
            const decodedString = he.decode(trimmedString);

            return /^[A-Za-z0-9.\s\-&']+$/i.test(decodedString);
        }

        return false;
    }

    /**
     * Is var a string containing alpha numeric chars?
     *
     * @param {string} variable
     *
     * @returns {boolean}
     */
    validateAlphaNumericString(variable: string): boolean {
        const oThis = this;
        if (oThis.isVarNullOrUndefined(variable)) {
            return false;
        }

        return /^[a-z0-9]+$/i.test(variable);
    }

    /**
     * Is var a string containing alpha numeric chars ?
     *
     * @param {string} variable
     *
     * @returns {boolean}
     */
    validateAlphaNumericCommonSpecialCharString(variable: string): boolean {
        const oThis = this;
        if (oThis.isVarNullOrUndefined(variable)) {
            return false;
        }

        return /^[a-z0-9\_\- ]+$/i.test(variable);
    }

    /**
     * Is valid integer array?
     *
     * @param {array} array
     *
     * @returns {boolean}
     */
    validateIntegerArray(array: number[]): boolean {
        const oThis = this;
        if (Array.isArray(array)) {
            for (let index = 0; index < array.length; index++) {
                if (!oThis.validateInteger(array[index])) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    /**
     * Is valid non zero integer array?
     *
     * @param {array} array
     *
     * @returns {boolean}
     */
    validateNonZeroIntegerArray(array: number[]): boolean {
        const oThis = this;
        if (Array.isArray(array)) {
            for (let index = 0; index < array.length; index++) {
                if (!oThis.validateNonZeroInteger(array[index])) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    /**
     * Validate alpha numeric string array.
     *
     * @param {array} array
     *
     * @return {boolean}
     */
    validateAlphaNumericStringArray(array: string[]): boolean {
        const oThis = this;
        if (Array.isArray(array)) {
            for (let index = 0; index < array.length; index++) {
                if (!oThis.validateAlphaNumericString(array[index])) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    /**
     *  Is valid array?
     *
     *  @param {array} array
     *
     *  @returns {boolean}
     */
    validateArray(array: object[]): boolean {
        return Array.isArray(array);
    }

    /**
     *  Is valid non-empty array?
     *
     *  @param {array} array
     *
     *  @returns {boolean}
     */
    validateNonEmptyArray(array: string[]): boolean {
        return Array.isArray(array) && array.length > 0;
    }

    /**
     *  Is valid non-empty string array?
     *
     *  @param {array} array
     *
     *  @returns {boolean}
     */
    validateNonEmptyStringArray(array: string[]): boolean {
        const oThis = this;

        if (oThis.validateNonEmptyArray(array)) {
            for (let index = 0; index < array.length; index++) {
                if (typeof array[index] !== 'string') {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check if variable is object and non-empty.
     *
     * @param {object} variable
     *
     * @returns {boolean}
     */
    validateNonEmptyObject(variable: object): boolean {
        const oThis = this;
        if (oThis.isVarNullOrUndefined(variable) || typeof variable !== 'object') {
            return false;
        }

        for (const prop in variable) {
            try {
                if (Object.prototype.hasOwnProperty.call(variable, prop)) {
                    return true;
                }
            } catch (error) {
                return false;
            }
        }

        return false;
    }

    /**
     * Validate object.
     *
     * @param {object} variable
     *
     * @returns {boolean}
     */
    validateObject(variable: object): boolean {
        const oThis = this;
        return !(oThis.isVarNullOrUndefined(variable) || typeof variable !== 'object');
    }

    /**
     * Validate API validateTransactionStatusArray
     *
     * @param {array<string>} array
     *
     * @returns {boolean}
     */
    validateStringArray(array: string[]) {
        const oThis = this;
        if (Array.isArray(array)) {
            for (let index = 0; index < array.length; index++) {
                if (!oThis.validateString(array[index])) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    /**
     * Validate non-empty string.
     *
     * @param {string} variable
     *
     * @returns {boolean}
     */
    validateNonEmptyString(variable: string): boolean {
        const oThis = this;
        return !!(oThis.validateString(variable) && variable && variable.length !== 0);
    }

    /**
     * Validates a website url is correctly formed
     *
     * @param url
     * @returns {boolean}
     */
    validateGenericUrl(url: string): boolean {
        const oThis = this;
        if (url == '') {
            return true;
        }

        if (!oThis.validateString(url)) {
            return false;
        }

        return /^(http(s)?:\/\/)?([a-z0-9-_]{1,256}\.)+[a-z]{2,15}\b([a-z0-9@:%_+.\[\]\-{}!'",~#?&;/=*]*)$/i.test(url);
    }

    /**
     * Validates a website url is correctly formed
     *
     * @param url
     * @returns {boolean}
     */
    validateNonEmptyUrl(url: string): boolean {
        const oThis = this;
        if (url == '') {
            return false;
        }

        return oThis.validateGenericUrl(url);
    }

    /**
     * Validates null or string.
     *
     * @param string
     * @returns {boolean}
     */
    validateNullString(string: string): boolean {
        const oThis = this;
        if (oThis.isVarNull(string)) {
            return true;
        }

        return oThis.validateString(string);
    }

    /**
     * Validates a website url is correctly formed
     * NOTE: - checks for 'http' mandatorily.
     *
     * @param details
     * @returns {boolean}
     */
    validateHttpBasedUrl(details: string): boolean {
        const oThis = this;
        if (details == '') {
            return true;
        }

        return oThis.validateNonEmptyHttpBasedUrl(details);
    }

    /**
     * Validates a website url is correctly formed.
     * NOTE: - checks for 'http' mandatorily.
     *
     * @param details
     * @returns {boolean}
     */
    validateNonEmptyHttpBasedUrl(details: string): boolean {
        const oThis = this;
        if (!oThis.validateString(details)) {
            return false;
        }

        return /^http(s)?:\/\/([a-zA-Z0-9-_@:%+~#=]{1,256}\.)+[a-z]{2,15}\b([a-zA-Z0-9@:%_+.\[\]\-{}!'",~#?&;/=*]*)$/i.test(
            details,
        );
    }

    /**
     * Is variable number?
     *
     * @param {number} variable
     *
     * @returns {boolean}
     */
    validateNumber(variable: string | number): boolean {
        try {
            if (typeof variable !== 'string' && typeof variable !== 'number') {
                return false;
            }

            const variableInBn = new BigNumber(String(variable));
            // Variable is number and its length is less than 37 digits
            if (variableInBn.toString(10).length <= 37) {
                return true;
            }
        } catch (err) {
            console.error(err);
        }

        return false;
    }

    /**
     * Is var a valid email?
     *
     * @param {string} variable
     *
     * @returns {boolean}
     */
    isValidEmail(variable: string): boolean {
        const oThis = this;
        return (
            oThis.validateString(variable)
            && /^[A-Z0-9]+[A-Z0-9_%+-]*(\.[A-Z0-9_%+-]{1,})*@(?:[A-Z0-9](?:[A-Z0-9-]*[A-Z0-9])?\.)+[A-Z]{2,24}$/i.test(variable)
        );
    }

    /**
     * Is integer zero?
     *
     * @param {string/number} variable
     *
     * @returns {boolean}
     */
    validateZeroInteger(variable: number): boolean {
        const oThis = this;

        if (oThis.validateInteger(variable)) {
            return Number(variable) === 0;
        }

        return false;
    }

    /**
     * Is var a valid password string?
     *
     * @param {string} variable
     *
     * @returns {boolean}
     */
    validatePassword(variable: string): boolean {
        return variable.length <= 100 && variable.length >= 8;
    }

    /**
     * Validate if string has stop words.
     *
     * @param {string} string
     *
     * @return {boolean}
     */
    validateStopWords(string: string): boolean {
        if (typeof string !== 'string') {
            return false;
        }

        const reg_ex = /\b(?:anal|anus|arse|ballsack|bitch|biatch|blowjob|blow job|bollock|bollok|boner|boob|bugger|bum|butt|buttplug|clitoris|cock|coon|crap|cunt|dick|dildo|dyke|fag|feck|fellate|fellatio|felching|fuck|f u c k|fudgepacker|fudge packer|flange|Goddamn|God damn|homo|jerk|Jew|jizz|Kike|knobend|knob end|labia|muff|nigger|nigga|penis|piss|poop|prick|pube|pussy|scrotum|sex|shit|s hit|sh1t|slut|smegma|spunk|tit|tosser|turd|twat|vagina|wank|whore|porn|admin|terms|privacy|faq)\b/i;

        return !reg_ex.test(string);
    }
}

export default new ValidateHelper();
