import Logger from '../Logger';
import GeneralConstant from "../../constant/GeneralConstant";

class GenericOpHelpler {
    logDateFormat(): string {
        const date = new Date();

        return (
            `${date.getFullYear()
            }-${date.getMonth() + 1
            }-${date.getDate()
            } ${date.getHours()
            }:${date.getMinutes()
            }:${date.getSeconds()
            }.${date.getMilliseconds()}`
        );
    }

    getCurrentTimestampInSeconds(): number {
        return Math.floor(new Date().getTime() / 1000);
    }

    getCurrentTimestampInMinutes(): number {
        return Math.floor(new Date().getTime() / (60 * 1000));
    }

    toTimestamp(date: Date): number {
        return ((new Date(date)).getTime() / 1000);
    }

    sleep(ms: number): Promise<any> {
        Logger.info(`Basic::sleep:Sleeping for ${ms} ms.`);
        return new Promise(((resolve) => {
            setTimeout(resolve, ms);
        }));
    }

    capitalizeFirstLetter(str: string): string {
        return str[0].toUpperCase() + str.substr(1);
    }

    isEmptyObject(obj: object) {
        for (const property in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, property)) {
                return false;
            }
        }

        return true;
    }
}

export default new GenericOpHelpler();

