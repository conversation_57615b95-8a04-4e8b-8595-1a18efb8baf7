import { Success, Error } from "../types/Response";

export class ResponseUtils {
    /**
     * Type guard to check if response is an Error
     */
    static isError(response: Success | Error): response is Error {
        return !response.success;
    }

    /**
     * Type guard to check if response is a Success
     */
    static isSuccess(response: Success | Error): response is Success {
        return response.success;
    }

    /**
     * Safely extract error message from Error response
     */
    static getErrorMessage(response: Success | Error): string {
        if (!response.success) {
            return response.errorData || "Unknown error";
        }
        return "No error";
    }

    /**
     * Safely extract data from Success response
     */
    static getData(response: Success | Error): any {
        if (response.success) {
            return response.data;
        }
        return null;
    }

    /**
     * Throw error if response is not successful
     */
    static throwIfError(response: Success | Error, prefix?: string): void {
        if (!response.success) {
            const errorMessage = response.errorData || "Unknown error";
            const fullMessage = prefix ? `${prefix}: ${errorMessage}` : errorMessage;
            throw new Error(fullMessage);
        }
    }
}

export default ResponseUtils;
