import { UserModelAttributes } from "./Model";

export interface ErrorConfig {
    parameter: string;
    message: string;
}

export interface APIParamValidatorInterface {
    parameter: string;
    kind?: string;
    validatorMethods: Record<string, string | null>[];
}

export interface ApiParamSignatureInterface {
    mandatory?: APIParamValidatorInterface[];
    optional?: APIParamValidatorInterface[];
}

export interface ApiParamsInterface {
    apiName: string;
    internalParams: Record<string, any>;
    externalParams: Record<string, any>;
}

export interface CustomParamsInRequest {
    currentUser?: UserModelAttributes;
    apiName?: string;
    redirectToCookieValue?: string;
    isAddEmailSource?: boolean;
}
