import PersonalityQuestion from "../../models/PersonalityQuestion";

export interface UserModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    email: string;
    username: string;
    password: string;
    name: string;
    mobileNumber?: string,
    verifiedNumber?: boolean;
    verifiedEmail?: boolean;
    referralCodeId?: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface SchoolModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    name: string;
    email: string;
    code: string;
    password: string;
    mobileNumber?: string,
    verifiedNumber?: boolean;
    verifiedEmail?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface PersonalityQuestionModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    question: string;
    personalityType: number;
    interestTags?: number[];
    createdAt?: Date;
    updatedAt?: Date;
}

export interface UserAssessmentModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    type: number;
    userId: number;
    status: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface PersonalityAssessmentSetModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    assessmentId: number;
    questionId: number;
    userId: number;
    userAnswer?: number; // Agree/Disagree
    createdAt?: Date;
    updatedAt?: Date;
}

export interface OccupationAssessmentSetModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    assessmentId: number;
    occupationOption1: number;
    occupationOption2: number;
    userId: number;
    userChoice: number; // Either of options
    createdAt?: Date;
    updatedAt?: Date;
}

export interface ExpertProfileModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    name: string;
    yearOfExp: number;
    personalityType: number[];
    occupationType: number[];
    userId: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface ReferralCodeModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    code: string;
    validTill: Date;
    associatedSchoolId: number;
    usagesCount: number;
    extraValidations?: any;
    createdAt?: Date;
}

export interface OtpVerificationModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    userId: number;
    otpCode: string;
    otpType: number; // 1 = email_verification, 2 = password_reset
    expiresAt: Date;
    isUsed: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface UserEducationModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    userId: number;
    educationType: number; // Reference to EDUCATION_TYPES constants

    // Institution details
    institutionName?: string;

    // Degree/Course details
    degreeName?: string;
    specialization?: string;

    // For high school - reference to education_streams table
    educationStreamId?: number;

    // Academic performance
    percentage?: number;
    grade?: string;

    // Duration
    startYear?: number;
    endYear?: number;

    // Status
    isCompleted?: boolean;

    // Additional details
    boardUniversity?: string;
    location?: string;

    // For certifications
    certificationAuthority?: string;
    certificateNumber?: string;
    expiryDate?: Date;

    // Metadata
    notes?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

