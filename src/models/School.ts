import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions, Op, Sequelize
} from 'sequelize';
import { SchoolModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";


class SchoolModel extends Model {
}

export default class School {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        SchoolModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'School',
                tableName: 'schools',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(school: SchoolModelAttributes): Promise<Success | Error> {
        let savedResult;
        try {
            Logger.debug(`School-create::Creating with: ${JSON.stringify(school)}`);
            savedResult = await SchoolModel.create(school);
            Logger.debug(`School-create::Saved school result::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`School-create::Error saving user record. Exception ${JSON.stringify(e)}`);
        }
        return {
            success: true,
            data: savedResult
        };
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING(100),
                allowNull: true,
            },
            email: {
                type: DataTypes.STRING(100),
                allowNull: false,
                unique: true,
            },
            code: {
                type: DataTypes.STRING(15),
                unique: true,
            },
            mobileNumber: {
                type: DataTypes.BIGINT,
                unique: true,
                allowNull: false,
            },
            verifiedEmail: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            verifiedNumber: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ["email"]
            },
            {
                unique: true,
                fields: ["code"]
            }
        ];
    }
}
