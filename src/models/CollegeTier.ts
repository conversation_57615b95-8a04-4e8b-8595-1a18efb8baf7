import { DataTypes, InitOptions, Model } from 'sequelize';

class CollegeTierModel extends Model {
}

export default class CollegeTier {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        CollegeTierModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'CollegeTier',
                tableName: 'college_tier',
                timestamps: true
            }
        );
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            occupation_type: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'expert_profile',
                    key: 'id',
                },
            },
            tier: {
                type: DataTypes.SMALLINT,
                allowNull: false,
            },
            start_salary: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            end_salary: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
        };
    }
} 