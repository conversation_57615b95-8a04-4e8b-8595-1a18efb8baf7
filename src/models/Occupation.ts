import sequelize, {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Sequelize,
    WhereOptions
} from 'sequelize';
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

interface OccupationModelAttributes {
    id: number;
    name: string;
    slug: string;
    personalityType: number;
    minimumQualification: number;
    createdAt: Date;
    updatedAt: Date;
}

class OccupationModel extends Model<OccupationModelAttributes> implements OccupationModelAttributes {
    public id!: number;
    public name!: string;
    public slug!: string;
    public personalityType!: number;
    public minimumQualification!: number;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class Occupation {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        OccupationModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'Occupation',
                tableName: 'occupations',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async findAll(options: any): Promise<OccupationModelAttributes[]> {
        try {
            const results = await OccupationModel.findAll(options);
            return results.map(result => result.get({ plain: true }));
        } catch (e: any) {
            Logger.error(`Occupation-findAll: Error finding records. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async getByPersonalityTypes(personalityTypes: number[]): Promise<OccupationModelAttributes[]> {
        try {
            Logger.debug(`Occupation-getByPersonalityTypes: Finding all occupations for profession types: ${JSON.stringify(personalityTypes)}`);

            const queryOptions = {
                where: {
                    personalityType: {
                        [sequelize.Op.in]: personalityTypes
                    }
                },
                raw: true
            };
            Logger.debug(`Occupation-getByPersonalityTypes: Query options: ${JSON.stringify(queryOptions)}`);

            const occupations = await OccupationModel.findAll(queryOptions);
            Logger.debug(`Occupation-getByPersonalityTypes: Found ${occupations.length} occupations`);

            if (occupations.length > 0) {
                Logger.info(`Occupation-getByPersonalityTypes: Successfully retrieved occupations`);
            } else {
                Logger.warn(`Occupation-getByPersonalityTypes: No occupations found for the given profession types`);
            }

            return occupations;
        } catch (error) {
            Logger.error(`Occupation-getByPersonalityTypes: Error getting occupations. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async getOccupationById(occupationIds: number[]): Promise<OccupationModelAttributes[]> {
        try {
            Logger.debug(`Occupation-getOccupationById: Validating occupation IDs: ${JSON.stringify(occupationIds)}`);

            // Get all occupations from the database
            const occupations = await OccupationModel.findAll({
                where: {
                    id: {
                        [sequelize.Op.in]: occupationIds
                    }
                },
                raw: true
            });
            Logger.debug(`Occupation-getOccupationById: Found ${occupations.length} occupations`);
            return occupations;
        } catch (error) {
            Logger.error(`Occupation-validateOccupationIds: Error validating occupation IDs. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    /**
     * Validates occupation IDs to ensure they exist in the database
     *
     * @param occupationIds Array of occupation IDs to validate
     * @returns Success or Error response
     */
    public async validateOccupationIds(occupationIds: number[]): Promise<Success | Error> {
        try {
            Logger.debug(`Occupation-validateOccupationIds: Validating occupation IDs: ${JSON.stringify(occupationIds)}`);

            // Check if occupationIds is an array and not empty
            if (!Array.isArray(occupationIds) || occupationIds.length === 0) {
                Logger.warn(`Occupation-validateOccupationIds: Invalid input - occupationIds must be a non-empty array`);
                return {
                    success: false,
                    errorData: {
                        message: 'At least one occupation ID is required',
                        parameter: 'occupationIds'
                    }
                };
            }

            // Get all occupations from the database
            const occupations = await this.getOccupationById(occupationIds);
            Logger.debug(`Occupation-validateOccupationIds: Found ${occupations.length} occupations out of ${occupationIds.length} requested`);

            // Check if all occupation IDs exist
            if (occupations.length !== occupationIds.length) {
                const foundIds = occupations.map(occ => occ.id);
                const invalidIds = occupationIds.filter(id => !foundIds.includes(id));

                Logger.warn(`Occupation-validateOccupationIds: Invalid occupation IDs found: ${JSON.stringify(invalidIds)}`);

                return {
                    success: false,
                    errorData: {
                        message: `Invalid occupation IDs: ${invalidIds.join(', ')}`,
                        parameter: 'occupationIds',
                        invalidIds
                    }
                };
            }

            Logger.info(`Occupation-validateOccupationIds: All occupation IDs are valid`);
            return {
                success: true,
                data: {
                    message: 'All occupation IDs are valid',
                    occupationIds
                }
            };
        } catch (error) {
            Logger.error(`Occupation-validateOccupationIds: Error validating occupation IDs. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async saveUserOccupationChoices(userId: number, occupationIds: number[], userOccupationChoice: any): Promise<Success | Error> {
        try {
            Logger.debug(`Occupation-saveUserOccupationChoices: Saving occupation choices for user ${userId}: ${JSON.stringify(occupationIds)}`);

            // Validate occupation IDs
            const validationResult = await this.validateOccupationIds(occupationIds);
            if (!validationResult.success) {
                return validationResult;
            }

            // Save the occupation choices
            await userOccupationChoice.updateOccupationList(userId, occupationIds);

            Logger.info(`Occupation-saveUserOccupationChoices: Successfully saved occupation choices for user ${userId}`);

            return {
                success: true,
                data: {
                    message: 'Occupation choices saved successfully',
                    userId,
                    occupationIds
                }
            };
        } catch (error) {
            Logger.error(`Occupation-saveUserOccupationChoices: Error saving occupation choices. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async getRandomOccupationsByPersonalityTypes(personalityTypes: number[], limit: number = 2): Promise<OccupationModelAttributes[] | null> {
        try {
            Logger.debug(`Occupation-getRandomOccupationsByPersonalityTypes: Finding random occupations for profession types: ${JSON.stringify(personalityTypes)}`);
            Logger.debug(`Occupation-getRandomOccupationsByPersonalityTypes: Limit set to: ${limit}`);

            const queryOptions = {
                where: {
                    personalityType: {
                        [sequelize.Op.in]: personalityTypes
                    }
                },
                order: sequelize.literal('RANDOM()'),
                limit: limit,
                raw: true
            };
            Logger.debug(`Occupation-getRandomOccupationsByPersonalityTypes: Query options: ${JSON.stringify(queryOptions)}`);

            const occupations = await OccupationModel.findAll(queryOptions);
            Logger.debug(`Occupation-getRandomOccupationsByPersonalityTypes: Found ${occupations.length} occupations`);

            if (occupations.length > 0) {
                Logger.info(`Occupation-getRandomOccupationsByPersonalityTypes: Successfully retrieved occupations: ${JSON.stringify(occupations)}`);
            } else {
                Logger.warn(`Occupation-getRandomOccupationsByPersonalityTypes: No occupations found for the given profession types`);
            }

            return occupations;
        } catch (error) {
            Logger.error(`Occupation-getRandomOccupationsByPersonalityTypes: Error getting random occupations. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING(100),
                allowNull: true,
            },
            slug: {
                type: DataTypes.STRING(100),
                allowNull: true,
            },
            personalityType: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            minimumQualification: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["slug"]
            }
        ];
    }
}
