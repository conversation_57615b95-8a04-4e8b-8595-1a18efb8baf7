import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Op,
    WhereOptions
} from 'sequelize';
import { ReferralCodeModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

class ReferralCodeModel extends Model<ReferralCodeModelAttributes> implements ReferralCodeModelAttributes {
    public id!: number;
    public code!: string;
    public validTill!: Date;
    public associatedSchoolId!: number;
    public usagesCount!: number;
    public extraValidations?: any;
    public createdAt!: Date;
}

export default class ReferralCode {
    public model: typeof ReferralCodeModel;

    public constructor(initOptions: InitOptions) {
        const oThis = this;
        this.model = ReferralCodeModel;
        ReferralCodeModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'ReferralCode',
                tableName: 'referral_code',
                timestamps: true,
                updatedAt: false,
                indexes: oThis.getIndexes()
            },
        );
    }

    public async create(referralCode: ReferralCodeModelAttributes): Promise<Success | Error> {
        let savedResult;
        try {
            Logger.debug(`ReferralCode::create::Creating with: ${JSON.stringify(referralCode)}`);
            savedResult = await ReferralCodeModel.create(referralCode);
            Logger.debug(`ReferralCode::create::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`ReferralCode::create::Error saving referral code record. Exception ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
        return {
            success: true,
            data: savedResult
        };
    }

    public async findById(id: number): Promise<Success | Error> {
        let result;
        try {
            Logger.debug(`ReferralCode::findById::Finding referral code with id: ${id}`);
            result = await ReferralCodeModel.findByPk(id);
            Logger.debug(`ReferralCode::findById::result::${JSON.stringify(result)}`);
        } catch (e: any) {
            Logger.error(`ReferralCode::findById::Error finding referral code. Exception ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
        return {
            success: true,
            data: result
        };
    }

    public async findBySchoolId(schoolId: number): Promise<Success | Error> {
        let result;
        try {
            Logger.debug(`ReferralCode::findBySchoolId::Finding referral codes for school: ${schoolId}`);
            result = await ReferralCodeModel.findAll({
                where: {
                    associatedSchoolId: schoolId
                }
            });
            Logger.debug(`ReferralCode::findBySchoolId::result::${JSON.stringify(result)}`);
        } catch (e: any) {
            Logger.error(`ReferralCode::findBySchoolId::Error finding referral codes. Exception ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
        return {
            success: true,
            data: result
        };
    }

    public async findValidCodes(schoolId?: number): Promise<Success | Error> {
        let result;
        try {
            const whereClause: WhereOptions = {
                validTill: {
                    [Op.gte]: new Date()
                }
            };

            if (schoolId) {
                whereClause.associatedSchoolId = schoolId;
            }

            Logger.debug(`ReferralCode::findValidCodes::Finding valid referral codes with where: ${JSON.stringify(whereClause)}`);
            result = await ReferralCodeModel.findAll({
                where: whereClause
            });
            Logger.debug(`ReferralCode::findValidCodes::result::${JSON.stringify(result)}`);
        } catch (e: any) {
            Logger.error(`ReferralCode::findValidCodes::Error finding valid referral codes. Exception ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
        return {
            success: true,
            data: result
        };
    }

    public async findByCode(code: string): Promise<Success | Error> {
        let result;
        try {
            Logger.debug(`ReferralCode::findByCode::Finding referral code with code: ${code}`);
            result = await ReferralCodeModel.findOne({
                where: {
                    code: code,
                    validTill: {
                        [Op.gte]: new Date() // Only return valid (non-expired) codes
                    }
                }
            });
            Logger.debug(`ReferralCode::findByCode::result::${JSON.stringify(result)}`);
        } catch (e: any) {
            Logger.error(`ReferralCode::findByCode::Error finding referral code. Exception ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
        return {
            success: true,
            data: result
        };
    }

    public async incrementUsage(id: number): Promise<Success | Error> {
        let result;
        try {
            Logger.debug(`ReferralCode::incrementUsage::Incrementing usage for referral code: ${id}`);
            result = await ReferralCodeModel.increment('usagesCount', {
                where: { id }
            });
            Logger.debug(`ReferralCode::incrementUsage::result::${JSON.stringify(result)}`);
        } catch (e: any) {
            Logger.error(`ReferralCode::incrementUsage::Error incrementing usage. Exception ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
        return {
            success: true,
            data: result
        };
    }

    public async updateExtraValidations(id: number, extraValidations: any): Promise<Success | Error> {
        let result;
        try {
            Logger.debug(`ReferralCode::updateExtraValidations::Updating extra validations for referral code: ${id}`);
            result = await ReferralCodeModel.update(
                { extraValidations },
                { where: { id } }
            );
            Logger.debug(`ReferralCode::updateExtraValidations::result::${JSON.stringify(result)}`);
        } catch (e: any) {
            Logger.error(`ReferralCode::updateExtraValidations::Error updating extra validations. Exception ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
        return {
            success: true,
            data: result
        };
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            code: {
                type: DataTypes.STRING(20),
                allowNull: false,
                unique: true,
                field: 'code'
            },
            validTill: {
                type: DataTypes.DATE,
                allowNull: false,
                field: 'valid_till'
            },
            associatedSchoolId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'schools' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
                field: 'associated_school_id'
            },
            usagesCount: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 0,
                field: 'usages_count'
            },
            extraValidations: {
                type: DataTypes.JSON,
                allowNull: true,
                field: 'extra_validations'
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'created_at'
            },
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["associated_school_id"]
            },
            {
                unique: false,
                fields: ["valid_till"]
            }
        ];
    }
}
