import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Op,
    WhereOptions
} from 'sequelize';
import { UserModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

class UserModel extends Model {
}

export default class User {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        UserModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'User',
                tableName: 'users',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(user: UserModelAttributes): Promise<Success | Error> {
        let savedResult;
        try {
            Logger.debug(`User::create::Creating with: ${JSON.stringify(user)}`);
            savedResult = await UserModel.create(
                user,
            );

            Logger.debug(`User::save::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`UserModel::save::Error saving user record. Exception ${JSON.stringify(e)}`);
        }
        return {
            success: true,
            data: savedResult
        };
    }

    public async update(values: any, where: WhereOptions) {
        try {
            Logger.debug(`User-update: Values for update:${JSON.stringify(values)}, ${JSON.stringify(where)}`);
            const updateResult: any = await UserModel.update(values,
                {where}
             );

             Logger.debug(`User-update: Update result: ${JSON.stringify(updateResult)}`);
            if (updateResult.length === 0 || updateResult[0] < 1) {
                throw new Error("Update operation failed in User model");
            }
        } catch (e: any) {
            Logger.error(`User::update::Error updating user record. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async checkMobileNumberExists(mobileNumber: string, excludeUserId?: number): Promise<boolean> {
        try {
            Logger.debug(`User::checkMobileNumberExists::Checking mobile number: ${mobileNumber}, excluding user: ${excludeUserId}`);

            const whereClause: WhereOptions = {
                mobileNumber: mobileNumber
            };

            if (excludeUserId) {
                whereClause.id = { [Op.ne]: excludeUserId };
            }

            const existingUser = await UserModel.findOne({
                where: whereClause
            });

            const exists = !!existingUser;
            Logger.debug(`User::checkMobileNumberExists::Mobile number exists: ${exists}`);
            return exists;
        } catch (e: any) {
            Logger.error(`User::checkMobileNumberExists::Error checking mobile number. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async updateProfile(userId: number, updateData: Partial<UserModelAttributes>): Promise<void> {
        try {
            Logger.debug(`User::updateProfile::Updating user ${userId} with data: ${JSON.stringify(updateData)}`);

            // Add updated timestamp
            const dataWithTimestamp = {
                ...updateData,
                updatedAt: new Date()
            };

            await this.update(dataWithTimestamp, { id: userId });
            Logger.debug(`User::updateProfile::Successfully updated user ${userId}`);
        } catch (error: any) {
            Logger.error(`User::updateProfile::Error updating user profile: ${error.message}`);
            throw error;
        }
    }

    public async createUserWithEmail(email: string, schoolId?: number): Promise<UserModelAttributes> {
        try {
            Logger.debug(`User::createUserWithEmail::Creating user with email: ${email}, schoolId: ${schoolId}`);

            const userData: Partial<UserModelAttributes> = {
                email: email,
                verifiedEmail: false,
                verifiedNumber: false
            };

            // Add school ID if provided via referral code
            if (schoolId) {
                userData.referalCodeId = schoolId;
            }

            const savedResult = await UserModel.create(userData);
            Logger.debug(`User::createUserWithEmail::User created successfully with ID: ${JSON.stringify(savedResult)}, linked to school: ${schoolId}`);

            return this.convertToUserModelAttribute(savedResult);
        } catch (error: any) {
            Logger.error(`User::createUserWithEmail::Error creating user: ${error.message}`);
            throw error;
        }
    }

    public async getUserByEmailOrUsername(email: string, username: string = ""): Promise<UserModelAttributes | null> {
        try {
            const classInst = this;
            let user: any = await UserModel.findOne({
                where: {
                    [Op.or]: [
                        { username },
                        { email }
                    ]
                }
            });
            Logger.debug(`User-getUserByEmailOrUsername: User for username ${username} or email ${email} is: ${JSON.stringify(user)}`)
            return classInst.convertToUserModelAttribute(user)
        } catch (e: any) {
            Logger.info(`User-getUserByEmailOrUsername: Error getting user for username: ${username} or email ${email}. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    public async getUserByUsername(username: string): Promise<UserModelAttributes | null> {
        try {
            const classInst = this;
            let user: any = await UserModel.findOne({
                where: {
                    [Op.or]: [
                        { username },
                    ]
                }
            });
            Logger.debug(`User-getUserByUsername: User for username ${username}: ${JSON.stringify(user)}`)
            return classInst.convertToUserModelAttribute(user)
        } catch (e: any) {
            Logger.info(`User-getUserByUsername: Error getting user for username: ${username}. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    public async getUserByEmail(email: string): Promise<UserModelAttributes | null> {
        try {
            const classInst = this;
            let user: any = await UserModel.findOne({
                where: {
                    email: email
                }
            });
            Logger.debug(`User-getUserByEmail: User for email ${email}: ${JSON.stringify(user)}`)
            return classInst.convertToUserModelAttribute(user)
        } catch (e: any) {
            Logger.info(`User-getUserByEmail: Error getting user for email: ${email}. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    public async findByPk(id: number): Promise<UserModelAttributes | null> {
        try {
            const classInst = this;
            let user: any = await UserModel.findByPk(id);
            Logger.debug(`User-findByPk: User for id ${id}: ${JSON.stringify(user)}`)
            return classInst.convertToUserModelAttribute(user)
        } catch (e: any) {
            Logger.info(`User-findByPk: Error getting user for id: ${id}. Exception: ${JSON.stringify(e.message)}`)
            return null;
        }
    }

    private convertToUserModelAttribute(user: any) {
        let userModelObj: UserModelAttributes = {
            id: user.id,
            createdAt: user.createdAt,
            email: user.email,
            name: user.name,
            password: user.password,
            updatedAt: user.updatedAt,
            username: user.username,
            verifiedEmail: user.verifiedEmail,
            verifiedNumber: user.verifiedNumber,
            refreshToken: user.refreshToken,
            referralCodeId: user.referralCodeId
        }
        return userModelObj;
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            email: {
                type: DataTypes.STRING,
                allowNull: true,
                unique: true,
            },
            password: {
                type: DataTypes.STRING,
                allowNull: true,
                unique: true,
            },
            username: {
                type: DataTypes.STRING,
                allowNull: true,
                unique: true
            },
            refreshToken: {
                type: DataTypes.STRING,
                allowNull: true
            },
            mobileNumber: {
                type: DataTypes.STRING,
                allowNull: true,
                unique: true
            },
            verifiedEmail: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            verifiedNumber: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            referralCodeId: {
                type: DataTypes.INTEGER,
                allowNull: true,
                references: {
                    model: { tableName: 'referral_code' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'SET NULL',
                field: 'referral_code_id'
            },
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ["email"]
            }
        ];
    }
}

