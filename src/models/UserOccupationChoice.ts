import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Sequelize
} from 'sequelize';
import Logger from "../lib/Logger";

interface UserOccupationChoiceModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number;
    userId: number;
    occupationList: number[];
    createdAt: Date;
}

class UserOccupationChoiceModel extends Model {
}

export default class UserOccupationChoice {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        UserOccupationChoiceModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'UserOccupationChoice',
                tableName: 'user_occupation_choice',
                timestamps: true,
                updatedAt: false,
                indexes: oThis.getIndexes()
            },
        );
    }

    public async create(data: UserOccupationChoiceModelAttributes): Promise<UserOccupationChoiceModelAttributes> {
        try {
            Logger.debug(`UserOccupationChoice-create: Creating new record: ${JSON.stringify(data)}`);

            const newRecord = await UserOccupationChoiceModel.create(data);
            Logger.info(`UserOccupationChoice-create: Successfully created record with id: ${JSON.stringify(newRecord)}`);

            return newRecord.get({ plain: true });
        } catch (error) {
            Logger.error(`UserOccupationChoice-create: Error creating record. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    // TODO: Update return type to `UserOccupationChoiceModelAttributes | null`
    public async getByUserId(userId: number): Promise<any> {
        try {
            Logger.debug(`UserOccupationChoice-getByUserId: Finding record for user id: ${userId}`);

            const record = await UserOccupationChoiceModel.findOne({
                where: { userId },
                raw: true
            });

            if (record) {
                Logger.info(`UserOccupationChoice-getByUserId: Successfully retrieved record for user id: ${userId}`);
            } else {
                Logger.warn(`UserOccupationChoice-getByUserId: No record found for user id: ${userId}`);
            }

            return record;
        } catch (error) {
            Logger.error(`UserOccupationChoice-getByUserId: Error getting record. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async updateOccupationList(userId: number, occupationList: number[]): Promise<boolean> {
        try {
            Logger.debug(`UserOccupationChoice-updateOccupationList: Upserting occupation list for user id ${userId}: ${JSON.stringify(occupationList)}`);

            // Use Sequelize's upsert method for atomic upsert operation
            const [, created] = await UserOccupationChoiceModel.upsert(
                {
                    userId: userId,
                    occupationList: occupationList
                },
                {
                    conflictFields: ['user_id'], // Specify the field that determines uniqueness
                    returning: true
                }
            );

            if (created) {
                Logger.info(`UserOccupationChoice-updateOccupationList: Successfully created new record for user id: ${userId}`);
            } else {
                Logger.info(`UserOccupationChoice-updateOccupationList: Successfully updated existing record for user id: ${userId}`);
            }

            return true;
        } catch (error) {
            Logger.error(`UserOccupationChoice-updateOccupationList: Error upserting record. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                },
                field: 'user_id'
            },
            occupationList: {
                type: DataTypes.ARRAY(DataTypes.INTEGER),
                allowNull: false,
                defaultValue: [],
                field: 'occupation_list'
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
                field: 'created_at'
            }
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ['userId']
            }
        ];
    }
}
