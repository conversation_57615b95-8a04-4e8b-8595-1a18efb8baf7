import { DataTypes, InitOptions, Model } from 'sequelize';

class ExpertOccupationModel extends Model {
}

export default class ExpertOccupation {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        ExpertOccupationModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'ExpertOccupation',
                tableName: 'expert_occupation',
                timestamps: true
            }
        );
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            expert_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'expert_profile',
                    key: 'id',
                },
            },
            title: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            company: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            start_date: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            end_date: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            is_current: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
        };
    }
} 