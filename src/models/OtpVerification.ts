import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Op,
    WhereOptions
} from 'sequelize';
import { OtpVerificationModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

// OTP Type constants for database storage
export const OTP_TYPES = {
    EMAIL_VERIFICATION: 1,
    PASSWORD_RESET: 2
} as const;

export type OtpTypeString = 'email_verification' | 'password_reset';
export type OtpTypeNumber = typeof OTP_TYPES[keyof typeof OTP_TYPES];

// Helper functions to convert between string and number types
export const otpTypeToNumber = (type: OtpTypeString): OtpTypeNumber => {
    return type === 'email_verification' ? OTP_TYPES.EMAIL_VERIFICATION : OTP_TYPES.PASSWORD_RESET;
};

export const otpTypeToString = (type: OtpTypeNumber): OtpTypeString => {
    return type === OTP_TYPES.EMAIL_VERIFICATION ? 'email_verification' : 'password_reset';
};

class OtpVerificationModel extends Model<OtpVerificationModelAttributes> implements OtpVerificationModelAttributes {
    public id!: number;
    public userId!: number;
    public otpCode!: string;
    public otpType!: number; // 1 = email_verification, 2 = password_reset
    public expiresAt!: Date;
    public isUsed!: boolean;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class OtpVerification {
    public model: typeof OtpVerificationModel;

    public constructor(initOptions: InitOptions) {
        const oThis = this;
        this.model = OtpVerificationModel;
        OtpVerificationModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'OtpVerification',
                tableName: 'otp_verifications',
                timestamps: true,
                indexes: oThis.getIndexes()
            },
        );
    }

    public async generateOtp(userId: number, otpType: OtpTypeString = 'email_verification'): Promise<Success | Error> {
        try {
            Logger.debug(`OtpVerification::generateOtp::Generating OTP for userId: ${userId}, type: ${otpType}`);

            // Generate 6-digit OTP
            const otpCode = Math.floor(100000 + Math.random() * 900000).toString();

            // Set expiry time (10 minutes from now)
            const expiresAt = new Date();
            expiresAt.setMinutes(expiresAt.getMinutes() + 10);

            // Convert string type to number for database storage
            const otpTypeNumber = otpTypeToNumber(otpType);

            // Invalidate any existing OTPs for this user and type
            await this.invalidateExistingOtps(userId, otpType);

            // Create new OTP record
            const otpRecord = await OtpVerificationModel.create({
                userId,
                otpCode,
                otpType: otpTypeNumber,
                expiresAt,
                isUsed: false
            });

            Logger.debug(`OtpVerification::generateOtp::OTP generated successfully for userId: ${userId}`);
            return {
                success: true,
                data: {
                    otpCode, // In production, don't return this - send via email
                    expiresAt,
                    message: "OTP generated successfully"
                }
            };
        } catch (e: any) {
            Logger.error(`OtpVerification::generateOtp::Error generating OTP. Exception: ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
    }

    public async verifyOtp(userId: number, otpCode: string, otpType: OtpTypeString = 'email_verification'): Promise<Success | Error> {
        try {
            Logger.debug(`OtpVerification::verifyOtp::Verifying OTP for userId: ${userId}, type: ${otpType}`);

            // Convert a string type to number for a database query
            const otpTypeNumber = otpTypeToNumber(otpType);

            // Find the latest valid OTP for this user and type
            const otpRecord = await OtpVerificationModel.findOne({
                where: {
                    userId,
                    otpType: otpTypeNumber,
                    isUsed: false,
                    expiresAt: {
                        [Op.gt]: new Date()
                    }
                },
                order: [['createdAt', 'DESC']]
            });

            Logger.info(`OtpVerification::verifyOtp::OTP record: ${JSON.stringify(otpRecord)}`);

            if (!otpRecord) {
                return {
                    success: false,
                    errorData: "No valid OTP found or OTP has expired"
                };
            }

            Logger.debug(`OtpVerification::verifyOtp::OTP verified successfully for userId: ${userId}`);
            return {
                success: true,
                data: {
                    message: "OTP verified successfully"
                }
            };
        } catch (e: any) {
            Logger.error(`OtpVerification::verifyOtp::Error verifying OTP. Exception: ${JSON.stringify(e)}`);
            return {
                success: false,
                errorData: e.message
            };
        }
    }

    public async invalidateExistingOtps(userId: number, otpType: OtpTypeString): Promise<void> {
        try {
            // Convert string type to number for database query
            const otpTypeNumber = otpTypeToNumber(otpType);

            await OtpVerificationModel.update(
                { isUsed: true },
                {
                    where: {
                        userId,
                        otpType: otpTypeNumber,
                        isUsed: false
                    }
                }
            );
            Logger.debug(`OtpVerification::invalidateExistingOtps::Invalidated existing OTPs for userId: ${userId}`);
        } catch (e: any) {
            Logger.error(`OtpVerification::invalidateExistingOtps::Error invalidating OTPs. Exception: ${JSON.stringify(e)}`);
            throw e;
        }
    }

    public async cleanupExpiredOtps(): Promise<void> {
        try {
            const result = await OtpVerificationModel.destroy({
                where: {
                    expiresAt: {
                        [Op.lt]: new Date()
                    }
                }
            });
            Logger.debug(`OtpVerification::cleanupExpiredOtps::Cleaned up ${result} expired OTP records`);
        } catch (e: any) {
            Logger.error(`OtpVerification::cleanupExpiredOtps::Error cleaning up expired OTPs. Exception: ${JSON.stringify(e)}`);
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
                field: 'user_id'
            },
            otpCode: {
                type: DataTypes.STRING(6),
                allowNull: false,
                field: 'otp_code'
            },
            otpType: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 1, // 1 = email_verification, 2 = password_reset
                field: 'otp_type'
            },
            expiresAt: {
                type: DataTypes.DATE,
                allowNull: false,
                field: 'expires_at'
            },
            isUsed: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: false,
                field: 'is_used'
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'created_at'
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'updated_at'
            },
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["user_id", "otp_type"]
            },
            {
                unique: false,
                fields: ["expires_at"]
            },
            {
                unique: false,
                fields: ["user_id", "is_used", "expires_at"]
            }
        ];
    }
}
