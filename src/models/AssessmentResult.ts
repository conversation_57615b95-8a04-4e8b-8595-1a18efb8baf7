import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Sequelize,
    WhereOptions
} from 'sequelize';
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

interface AssessmentResultModelAttributes {
    id?: number;
    assessment_id: number;
    user_id: number;
    result: number[];
    created_at?: Date;
    updated_at?: Date;
}

class AssessmentResultModel extends Model<AssessmentResultModelAttributes> implements AssessmentResultModelAttributes {
    public id!: number;
    public assessment_id!: number;
    public user_id!: number;
    public result!: number[];
    public created_at!: Date;
    public updated_at!: Date;
}

export default class AssessmentResult {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        AssessmentResultModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'AssessmentResult',
                tableName: 'assessment_result',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(assessmentResult: AssessmentResultModelAttributes): Promise<AssessmentResultModel> {
        let savedResult: AssessmentResultModel;
        try {
            // Check if result already exists for this assessment
            const existingResult = await this.getByUserAssessmentId(assessmentResult.assessment_id);

            if (existingResult) {
                Logger.debug(`AssessmentResult-create: Result already exists for assessment ${assessmentResult.assessment_id}. Updating existing record.`);
                // Update existing record
                await AssessmentResultModel.update(assessmentResult, {
                    where: {
                        assessment_id: assessmentResult.assessment_id
                    }
                });
                savedResult = await this.getByUserAssessmentId(assessmentResult.assessment_id) as AssessmentResultModel;
            } else {
                Logger.debug(`AssessmentResult-create: Creating new result for assessment ${assessmentResult.assessment_id}`);
                savedResult = await AssessmentResultModel.create(assessmentResult);
            }

            Logger.debug(`AssessmentResult-create: Saved/Updated results: ${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`AssessmentResult-create: Error saving assessment result record. Exception ${JSON.stringify(e)}`);
            throw e;
        }
        return savedResult;
    }

    public async getByUserAssessmentId(userAssessmentId: number): Promise<AssessmentResultModel | null> {
        try {
            const result = await AssessmentResultModel.findOne({
                where: {
                    assessment_id: userAssessmentId
                }
            });
            Logger.debug(`AssessmentResult-getByUserAssessmentId: Found result: ${JSON.stringify(result)}`);
            return result?.toJSON() as AssessmentResultModel | null;
        } catch (e: any) {
            Logger.error(`AssessmentResult-getByUserAssessmentId: Error getting assessment result. Exception: ${JSON.stringify(e)}`);
            return null;
        }
    }

    public async findOne(options: any): Promise<AssessmentResultModelAttributes | null> {
        try {
            const result = await AssessmentResultModel.findOne(options);
            return result ? result.get({ plain: true }) : null;
        } catch (e: any) {
            Logger.error(`AssessmentResult-findOne: Error finding record. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async getLatestAssessmentResult(userId: number): Promise<AssessmentResultModelAttributes | null> {
        try {
            const result = await AssessmentResultModel.findOne({
                where: {
                    user_id: userId
                },
                order: [['created_at', 'DESC']],
                raw: true
            });
            Logger.debug(`AssessmentResult-getLatestAssessmentResult: Found latest assessment result: ${JSON.stringify(result)}`);
            return result;
        } catch (error: any) {
            Logger.error(`AssessmentResult-getLatestAssessmentResult: Error getting latest assessment result. Exception: ${JSON.stringify(error.message)}`);
            throw error;
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            assessment_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                unique: true,
                references: {
                    model: 'user_assessments',
                    key: 'id'
                }
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                }
            },
            result: {
                type: DataTypes.ARRAY(DataTypes.INTEGER),
                allowNull: false
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            }
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                fields: ['assessment_id'],
                unique: true
            },
            {
                fields: ['user_id']
            }
        ];
    }
}
