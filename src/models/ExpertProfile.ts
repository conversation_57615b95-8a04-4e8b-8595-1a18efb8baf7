import { DataTypes, InitOptions, Model } from 'sequelize';
import { ExpertProfileModelAttributes } from "../lib/types/Model";

class ExpertProfileModel extends Model<ExpertProfileModelAttributes> implements ExpertProfileModelAttributes {
    public id!: number;
    public name!: string;
    public yearOfExp!: number;
    public personalityType!: number[];
    public occupationType!: number[];
    public userId!: number;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class ExpertProfile {
    public model: typeof ExpertProfileModel;

    public constructor(initOptions: InitOptions) {
        const oThis = this;
        this.model = ExpertProfileModel;
        ExpertProfileModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'ExpertProfile',
                tableName: 'expert_profile',
                timestamps: true
            }
        );
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            yearOfExp: {
                type: DataTypes.INTEGER,
                allowNull: false,
                field: 'year_of_exp'
            },
            personalityType: {
                type: DataTypes.ARRAY(DataTypes.INTEGER),
                allowNull: false,
                field: 'personality_type'
            },
            occupationType: {
                type: DataTypes.ARRAY(DataTypes.INTEGER),
                allowNull: false,
                field: 'occupation_type'
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
                field: 'user_id'
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'created_at'
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'updated_at'
            },
        };
    }
}
