import { DataTypes, InitOptions, Model } from 'sequelize';

class ExpertEducationModel extends Model {
}

export default class ExpertEducation {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        ExpertEducationModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'ExpertEducation',
                tableName: 'expert_education',
                timestamps: true
            }
        );
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            expert_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'expert_profile',
                    key: 'id',
                },
            },
            school_or_college: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            start_year: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            end_year: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
            },
        };
    }
} 