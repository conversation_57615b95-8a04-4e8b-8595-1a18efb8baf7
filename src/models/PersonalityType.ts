import sequelize, {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Sequelize,
    WhereOptions
} from 'sequelize';
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

interface PersonalityTypeModelAttributes {
    id: number;
    name: string;
    createdAt: Date;
    updatedAt: Date;
}

class PersonalityTypeModel extends Model<PersonalityTypeModelAttributes> implements PersonalityTypeModelAttributes {
    public id!: number;
    public name!: string;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class PersonalityType {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        PersonalityTypeModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'PersonalityType',
                tableName: 'personality_types',
                timestamps: true
            },
        );
    }

    public async findAll(options: any): Promise<PersonalityTypeModelAttributes[]> {
        try {
            const results = await PersonalityTypeModel.findAll(options);
            return results.map(result => result.get({ plain: true }));
        } catch (e: any) {
            Logger.error(`PersonalityType-findAll: Error finding records. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async getById(id: number): Promise<PersonalityTypeModelAttributes | null> {
        try {
            Logger.debug(`PersonalityType-getById: Finding profession type with id: ${id}`);

            const personalityType = await PersonalityTypeModel.findByPk(id, { raw: true });

            if (personalityType) {
                Logger.info(`PersonalityType-getById: Successfully retrieved profession type with id: ${id}`);
            } else {
                Logger.warn(`PersonalityType-getById: No profession type found with id: ${id}`);
            }

            return personalityType;
        } catch (error) {
            Logger.error(`PersonalityType-getById: Error getting profession type. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async getByIds(ids: number[]): Promise<PersonalityTypeModelAttributes[]> {
        try {
            Logger.debug(`PersonalityType-getByIds: Finding profession types with ids: ${JSON.stringify(ids)}`);

            const queryOptions = {
                where: {
                    id: {
                        [sequelize.Op.in]: ids
                    }
                },
                raw: true
            };

            const personalityTypes = await PersonalityTypeModel.findAll(queryOptions);
            Logger.debug(`PersonalityType-getByIds: Found ${personalityTypes.length} profession types`);

            if (personalityTypes.length > 0) {
                Logger.info(`PersonalityType-getByIds: Successfully retrieved profession types`);
            } else {
                Logger.warn(`PersonalityType-getByIds: No profession types found for the given ids`);
            }

            return personalityTypes;
        } catch (error) {
            Logger.error(`PersonalityType-getByIds: Error getting profession types. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async create(personalityType: PersonalityTypeModelAttributes): Promise<PersonalityTypeModelAttributes> {
        try {
            Logger.debug(`PersonalityType-create: Creating new profession type: ${JSON.stringify(personalityType)}`);

            const newPersonalityType = await PersonalityTypeModel.create(personalityType);
            Logger.info(`PersonalityType-create: Successfully created profession type with id: ${newPersonalityType.id}`);

            return newPersonalityType.get({ plain: true });
        } catch (error) {
            Logger.error(`PersonalityType-create: Error creating profession type. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async update(id: number, values: Partial<PersonalityTypeModelAttributes>): Promise<boolean> {
        try {
            Logger.debug(`PersonalityType-update: Updating profession type with id ${id}: ${JSON.stringify(values)}`);

            const [updatedRows] = await PersonalityTypeModel.update(values, {
                where: { id }
            });

            if (updatedRows > 0) {
                Logger.info(`PersonalityType-update: Successfully updated profession type with id: ${id}`);
                return true;
            } else {
                Logger.warn(`PersonalityType-update: No profession type found with id: ${id}`);
                return false;
            }
        } catch (error) {
            Logger.error(`PersonalityType-update: Error updating profession type. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async getPersonalityTypeNames(personalityTypeIds: number[]): Promise<Record<number, string>> {
        try {
            Logger.debug(`PersonalityType-getPersonalityTypeNames: Getting names for profession types: ${JSON.stringify(personalityTypeIds)}`);

            const personalityTypes = await PersonalityTypeModel.findAll({
                attributes: ['id', 'name'],
                where: {
                    id: {
                        [sequelize.Op.in]: personalityTypeIds
                    }
                },
                raw: true
            });

            Logger.debug(`PersonalityType-getPersonalityTypeNames: Found ${personalityTypes.length} profession types`);

            const result = personalityTypes.reduce((acc: Record<number, string>, type: any) => {
                acc[type.id] = type.name;
                return acc;
            }, {});

            if (Object.keys(result).length > 0) {
                Logger.info(`PersonalityType-getPersonalityTypeNames: Successfully retrieved profession type names`);
            } else {
                Logger.warn(`PersonalityType-getPersonalityTypeNames: No profession types found for the given ids`);
            }

            return result;
        } catch (error) {
            Logger.error(`PersonalityType-getPersonalityTypeNames: Error getting profession type names. Exception: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            name: {
                type: DataTypes.STRING(100),
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
                field: 'created_at'
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
                field: 'updated_at'
            },
        }
    }
}
