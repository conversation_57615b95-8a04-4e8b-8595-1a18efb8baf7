import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    WhereOptions
} from 'sequelize';
import { UserEducationModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";

class UserEducationModel extends Model<UserEducationModelAttributes> implements UserEducationModelAttributes {
    public id!: number;
    public userId!: number;
    public highSchoolStreamId?: number;
    public tenthPercentage?: number;
    public eleventhPercentage?: number;
    public twelfthPercentage?: number;
    public graduationDegree?: string;
    public graduationSpecialization?: string;
    public graduationCollege?: string;
    public graduationPercentage?: number;
    public graduationStartYear?: number;
    public graduationEndYear?: number;
    public postGraduationDegree?: string;
    public postGraduationSpecialization?: string;
    public postGraduationCollege?: string;
    public postGraduationPercentage?: number;
    public postGraduationStartYear?: number;
    public postGraduationEndYear?: number;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class UserEducation {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        UserEducationModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'UserEducation',
                tableName: 'user_education',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(userEducation: UserEducationModelAttributes): Promise<Success | Error> {
        let savedResult;
        try {
            Logger.debug(`UserEducation::create::Creating with: ${JSON.stringify(userEducation)}`);
            savedResult = await UserEducationModel.create(userEducation);
            Logger.debug(`UserEducation::create::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`UserEducation::create::Error saving user education record. Exception ${JSON.stringify(e)}`);
            throw e;
        }
        return {
            success: true,
            data: savedResult
        };
    }

    public async update(updateData: Partial<UserEducationModelAttributes>, whereClause: WhereOptions): Promise<void> {
        try {
            Logger.debug(`UserEducation::update::Updating with data: ${JSON.stringify(updateData)} where: ${JSON.stringify(whereClause)}`);
            
            const dataWithTimestamp = {
                ...updateData,
                updatedAt: new Date()
            };

            await UserEducationModel.update(dataWithTimestamp, { where: whereClause });
            Logger.debug(`UserEducation::update::Successfully updated user education`);
        } catch (error: any) {
            Logger.error(`UserEducation::update::Error updating user education: ${error.message}`);
            throw error;
        }
    }

    public async upsertUserEducation(userId: number, educationData: Partial<UserEducationModelAttributes>): Promise<void> {
        try {
            Logger.debug(`UserEducation::upsertUserEducation::Upserting for user ${userId} with data: ${JSON.stringify(educationData)}`);

            const existingEducation = await this.getUserEducation(userId);
            
            if (existingEducation) {
                // Update existing record
                await this.update(educationData, { userId: userId });
            } else {
                // Create new record
                const newEducationData = {
                    ...educationData,
                    userId: userId
                };
                await this.create(newEducationData as UserEducationModelAttributes);
            }

            Logger.debug(`UserEducation::upsertUserEducation::Successfully upserted education for user ${userId}`);
        } catch (error: any) {
            Logger.error(`UserEducation::upsertUserEducation::Error upserting user education: ${error.message}`);
            throw error;
        }
    }

    public async getUserEducation(userId: number): Promise<UserEducationModelAttributes | null> {
        try {
            Logger.debug(`UserEducation::getUserEducation::Getting education for user ${userId}`);
            
            const education = await UserEducationModel.findOne({
                where: { userId: userId }
            });

            if (education) {
                return this.convertToUserEducationModelAttribute(education);
            }
            
            return null;
        } catch (error: any) {
            Logger.error(`UserEducation::getUserEducation::Error getting user education: ${error.message}`);
            throw error;
        }
    }

    private convertToUserEducationModelAttribute(education: any): UserEducationModelAttributes {
        return {
            id: education.id,
            userId: education.userId,
            highSchoolStreamId: education.highSchoolStreamId,
            tenthPercentage: education.tenthPercentage,
            eleventhPercentage: education.eleventhPercentage,
            twelfthPercentage: education.twelfthPercentage,
            graduationDegree: education.graduationDegree,
            graduationSpecialization: education.graduationSpecialization,
            graduationCollege: education.graduationCollege,
            graduationPercentage: education.graduationPercentage,
            graduationStartYear: education.graduationStartYear,
            graduationEndYear: education.graduationEndYear,
            postGraduationDegree: education.postGraduationDegree,
            postGraduationSpecialization: education.postGraduationSpecialization,
            postGraduationCollege: education.postGraduationCollege,
            postGraduationPercentage: education.postGraduationPercentage,
            postGraduationStartYear: education.postGraduationStartYear,
            postGraduationEndYear: education.postGraduationEndYear,
            createdAt: education.createdAt,
            updatedAt: education.updatedAt
        };
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
                unique: true,
                field: 'user_id'
            },
            highSchoolStreamId: {
                type: DataTypes.INTEGER,
                allowNull: true,
                references: {
                    model: { tableName: 'education_streams' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'SET NULL',
                field: 'high_school_stream_id'
            },
            tenthPercentage: {
                type: DataTypes.DECIMAL(5, 2),
                allowNull: true,
                field: 'tenth_percentage'
            },
            eleventhPercentage: {
                type: DataTypes.DECIMAL(5, 2),
                allowNull: true,
                field: 'eleventh_percentage'
            },
            twelfthPercentage: {
                type: DataTypes.DECIMAL(5, 2),
                allowNull: true,
                field: 'twelfth_percentage'
            },
            graduationDegree: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'graduation_degree'
            },
            graduationSpecialization: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'graduation_specialization'
            },
            graduationCollege: {
                type: DataTypes.STRING(200),
                allowNull: true,
                field: 'graduation_college'
            },
            graduationPercentage: {
                type: DataTypes.DECIMAL(5, 2),
                allowNull: true,
                field: 'graduation_percentage'
            },
            graduationStartYear: {
                type: DataTypes.INTEGER,
                allowNull: true,
                field: 'graduation_start_year'
            },
            graduationEndYear: {
                type: DataTypes.INTEGER,
                allowNull: true,
                field: 'graduation_end_year'
            },
            postGraduationDegree: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'post_graduation_degree'
            },
            postGraduationSpecialization: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'post_graduation_specialization'
            },
            postGraduationCollege: {
                type: DataTypes.STRING(200),
                allowNull: true,
                field: 'post_graduation_college'
            },
            postGraduationPercentage: {
                type: DataTypes.DECIMAL(5, 2),
                allowNull: true,
                field: 'post_graduation_percentage'
            },
            postGraduationStartYear: {
                type: DataTypes.INTEGER,
                allowNull: true,
                field: 'post_graduation_start_year'
            },
            postGraduationEndYear: {
                type: DataTypes.INTEGER,
                allowNull: true,
                field: 'post_graduation_end_year'
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'created_at'
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'updated_at'
            },
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                fields: ["userId"]
            },
            {
                fields: ["highSchoolStreamId"]
            }
        ];
    }
}
