import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    WhereOptions
} from 'sequelize';
import { UserEducationModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import Logger from "../lib/Logger";
import { EDUCATION_TYPES, EducationType } from "../constant/EducationConstants";

class UserEducationModel extends Model<UserEducationModelAttributes> implements UserEducationModelAttributes {
    public id!: number;
    public userId!: number;
    public educationType!: number;
    public institutionName?: string;
    public degreeName?: string;
    public specialization?: string;
    public educationStreamId?: number;
    public percentage?: number;
    public grade?: string;
    public startYear?: number;
    public endYear?: number;
    public isCompleted?: boolean;
    public boardUniversity?: string;
    public location?: string;
    public certificationAuthority?: string;
    public certificateNumber?: string;
    public expiryDate?: Date;
    public notes?: string;
    public createdAt!: Date;
    public updatedAt!: Date;
}

export default class UserEducation {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        UserEducationModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'UserEducation',
                tableName: 'user_education',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(userEducation: UserEducationModelAttributes): Promise<Success | Error> {
        let savedResult;
        try {
            Logger.debug(`UserEducation::create::Creating with: ${JSON.stringify(userEducation)}`);
            savedResult = await UserEducationModel.create(userEducation);
            Logger.debug(`UserEducation::create::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`UserEducation::create::Error saving user education record. Exception ${JSON.stringify(e)}`);
            throw e;
        }
        return {
            success: true,
            data: savedResult
        };
    }

    public async update(updateData: Partial<UserEducationModelAttributes>, whereClause: WhereOptions): Promise<void> {
        try {
            Logger.debug(`UserEducation::update::Updating with data: ${JSON.stringify(updateData)} where: ${JSON.stringify(whereClause)}`);
            
            const dataWithTimestamp = {
                ...updateData,
                updatedAt: new Date()
            };

            await UserEducationModel.update(dataWithTimestamp, { where: whereClause });
            Logger.debug(`UserEducation::update::Successfully updated user education`);
        } catch (error: any) {
            Logger.error(`UserEducation::update::Error updating user education: ${error.message}`);
            throw error;
        }
    }

    public async upsertUserEducation(userId: number, educationData: Partial<UserEducationModelAttributes>): Promise<void> {
        try {
            Logger.debug(`UserEducation::upsertUserEducation::Upserting for user ${userId} with data: ${JSON.stringify(educationData)}`);

            if (!educationData.educationType) {
                throw new Error('Education type is required for upsert operation');
            }

            // For high school, we allow only one record per user
            if (educationData.educationType === EDUCATION_TYPES.HIGH_SCHOOL) {
                const existingHighSchool = await this.getUserEducationByType(userId, EDUCATION_TYPES.HIGH_SCHOOL);

                if (existingHighSchool) {
                    // Update existing high school record
                    await this.update(educationData, {
                        userId: userId,
                        educationType: EDUCATION_TYPES.HIGH_SCHOOL
                    });
                } else {
                    // Create new high school record
                    const newEducationData = {
                        ...educationData,
                        userId: userId
                    };
                    await this.create(newEducationData as UserEducationModelAttributes);
                }
            } else {
                // For other education types, always create new records
                const newEducationData = {
                    ...educationData,
                    userId: userId
                };
                await this.create(newEducationData as UserEducationModelAttributes);
            }

            Logger.debug(`UserEducation::upsertUserEducation::Successfully upserted education for user ${userId}`);
        } catch (error: any) {
            Logger.error(`UserEducation::upsertUserEducation::Error upserting user education: ${error.message}`);
            throw error;
        }
    }

    public async getUserEducation(userId: number): Promise<UserEducationModelAttributes[]> {
        try {
            Logger.debug(`UserEducation::getUserEducation::Getting all education records for user ${userId}`);

            const educationRecords = await UserEducationModel.findAll({
                where: { userId: userId },
                order: [['educationType', 'ASC'], ['startYear', 'ASC']]
            });

            return educationRecords.map(record => this.convertToUserEducationModelAttribute(record));
        } catch (error: any) {
            Logger.error(`UserEducation::getUserEducation::Error getting user education: ${error.message}`);
            throw error;
        }
    }

    public async getUserEducationByType(userId: number, educationType: EducationType): Promise<UserEducationModelAttributes | null> {
        try {
            Logger.debug(`UserEducation::getUserEducationByType::Getting education type ${educationType} for user ${userId}`);

            const education = await UserEducationModel.findOne({
                where: {
                    userId: userId,
                    educationType: educationType
                }
            });

            if (education) {
                return this.convertToUserEducationModelAttribute(education);
            }

            return null;
        } catch (error: any) {
            Logger.error(`UserEducation::getUserEducationByType::Error getting user education by type: ${error.message}`);
            throw error;
        }
    }

    public async deleteUserEducation(userId: number, educationId: number): Promise<void> {
        try {
            Logger.debug(`UserEducation::deleteUserEducation::Deleting education ${educationId} for user ${userId}`);

            await UserEducationModel.destroy({
                where: {
                    id: educationId,
                    userId: userId
                }
            });

            Logger.debug(`UserEducation::deleteUserEducation::Successfully deleted education ${educationId} for user ${userId}`);
        } catch (error: any) {
            Logger.error(`UserEducation::deleteUserEducation::Error deleting user education: ${error.message}`);
            throw error;
        }
    }

    private convertToUserEducationModelAttribute(education: any): UserEducationModelAttributes {
        return {
            id: education.id,
            userId: education.userId,
            educationType: education.educationType,
            institutionName: education.institutionName,
            degreeName: education.degreeName,
            specialization: education.specialization,
            educationStreamId: education.educationStreamId,
            percentage: education.percentage,
            grade: education.grade,
            startYear: education.startYear,
            endYear: education.endYear,
            isCompleted: education.isCompleted,
            boardUniversity: education.boardUniversity,
            location: education.location,
            certificationAuthority: education.certificationAuthority,
            certificateNumber: education.certificateNumber,
            expiryDate: education.expiryDate,
            notes: education.notes,
            createdAt: education.createdAt,
            updatedAt: education.updatedAt
        };
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: { tableName: 'users' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'CASCADE',
                field: 'user_id'
            },
            educationType: {
                type: DataTypes.INTEGER,
                allowNull: false,
                field: 'education_type'
            },
            institutionName: {
                type: DataTypes.STRING(200),
                allowNull: true,
                field: 'institution_name'
            },
            degreeName: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'degree_name'
            },
            specialization: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'specialization'
            },
            educationStreamId: {
                type: DataTypes.INTEGER,
                allowNull: true,
                references: {
                    model: { tableName: 'education_streams' },
                    key: 'id'
                },
                onUpdate: 'CASCADE',
                onDelete: 'SET NULL',
                field: 'education_stream_id'
            },
            percentage: {
                type: DataTypes.DECIMAL(5, 2),
                allowNull: true,
                field: 'percentage'
            },
            grade: {
                type: DataTypes.STRING(10),
                allowNull: true,
                field: 'grade'
            },
            startYear: {
                type: DataTypes.INTEGER,
                allowNull: true,
                field: 'start_year'
            },
            endYear: {
                type: DataTypes.INTEGER,
                allowNull: true,
                field: 'end_year'
            },
            isCompleted: {
                type: DataTypes.BOOLEAN,
                allowNull: false,
                defaultValue: true,
                field: 'is_completed'
            },
            boardUniversity: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'board_university'
            },
            location: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'location'
            },
            certificationAuthority: {
                type: DataTypes.STRING(100),
                allowNull: true,
                field: 'certification_authority'
            },
            certificateNumber: {
                type: DataTypes.STRING(50),
                allowNull: true,
                field: 'certificate_number'
            },
            expiryDate: {
                type: DataTypes.DATE,
                allowNull: true,
                field: 'expiry_date'
            },
            notes: {
                type: DataTypes.TEXT,
                allowNull: true,
                field: 'notes'
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'created_at'
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW,
                field: 'updated_at'
            },
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                fields: ["userId"]
            },
            {
                fields: ["educationType"]
            },
            {
                fields: ["userId", "educationType"]
            },
            {
                fields: ["educationStreamId"]
            }
        ];
    }
}
