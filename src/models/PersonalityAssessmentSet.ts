import {
    DataTypes,
    InitOptions,
    Model,
    ModelIndexesOptions,
    Op,
    Sequelize,
    WhereOptions
} from 'sequelize';
import { PersonalityAssessmentSetModelAttributes } from "../lib/types/Model";
import Logger from "../lib/Logger";
import ResponseHelper from "../middlewares/ResponseHelper";


class PersonalityAssessmentSetModel extends Model {
}


export default class PersonalityAssessmentSet {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        PersonalityAssessmentSetModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: 'PersonalityAssessmentSet',
                tableName: 'personality_assessment_set',
                indexes: oThis.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(personalityAssessmentSet: PersonalityAssessmentSetModelAttributes): Promise<PersonalityAssessmentSetModelAttributes> {
        let savedResult = {};
        try {
            savedResult = await PersonalityAssessmentSetModel.create(personalityAssessmentSet);
            Logger.debug(`PersonalityAssessmentSet-create::Saved personality questions: ${JSON.stringify(savedResult)}`);
        } catch (error: any) {
            Logger.error(`PersonalityAssessmentSet-create::Error saving personality questions set. Exception ${JSON.stringify(error.message)}`);
        }
        return savedResult as PersonalityAssessmentSetModelAttributes
    }

    public async bulkCreate(personalityAssessmentSet: PersonalityAssessmentSetModelAttributes[]): Promise<Record<number,  { id: number }> | undefined> {
        try {
            let savedResults;
            savedResults = await PersonalityAssessmentSetModel.bulkCreate(personalityAssessmentSet);
            Logger.debug(`PersonalityAssessmentSet-bulkCreate::Saved personality questions set::${JSON.stringify(savedResults)}`);
            // Format response

            let recordByQuestionId: Record<number, { id: number }> = {}
            for (let idx = 0; idx < savedResults.length; idx++) {
                let newRecord: any = savedResults[idx]
                recordByQuestionId[newRecord.questionId] = {
                    id: newRecord.id,
                }
            }
            Logger.debug(`PersonalityAssessmentSet-bulkCreate::Assessment set response: ${JSON.stringify(recordByQuestionId)}`);
            return recordByQuestionId;
        } catch (e: any) {
            Logger.error(`PersonalityAssessmentSet-bulkCreate::Error saving assessment set. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputTokenAddressMap: JSON.stringify(personalityAssessmentSet)
            }));
        }
    }

    public async findByAssessmentId(assessmentId: number): Promise<PersonalityAssessmentSetModelAttributes[]> {
        try {
            const results = await PersonalityAssessmentSetModel.findAll({ 
                where: { 
                    assessmentId: assessmentId
                }
            });
            Logger.debug(`PersonalityAssessmentSet::findAll::Results: ${JSON.stringify(results)}`);
            if (results.length === 0) {
                return [];
            }
            const formattedResult =  results.map(result => this.convertToModelAttributes(result));
            Logger.debug(`PersonalityAssessmentSet::findAll::Formatted results: ${JSON.stringify(formattedResult)}`);
            return formattedResult
        } catch (e: any) {
            Logger.error(`PersonalityAssessmentSet::findAll::Error finding records. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async findAll(where: WhereOptions): Promise<PersonalityAssessmentSetModelAttributes[]> {
        try {
            const results = await PersonalityAssessmentSetModel.findAll({ where });
            return results.map(result => this.convertToModelAttributes(result));
        } catch (e: any) {
            Logger.error(`PersonalityAssessmentSet::findAll::Error finding records. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async update(values: any, where: WhereOptions): Promise<void> {
        try {
            const updateResult: any = await PersonalityAssessmentSetModel.update(values, { where });
            if (updateResult.length === 0 || updateResult[0] < 1) {
                return Promise.reject(
                    ResponseHelper.error(['generalError'],
                        { error: 'Update operation failed', values: JSON.stringify(values), WhereOptions: JSON.stringify(where) }
                    ));
            }
        } catch (e: any) {
            Logger.error(`PersonalityAssessmentSet::update::Error updating record. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    public async findById(id: number): Promise<PersonalityAssessmentSetModelAttributes | null> {
        try {
            const result = await PersonalityAssessmentSetModel.findByPk(id);
            if (!result) {
                return null;
            }
            return this.convertToModelAttributes(result);
        } catch (e: any) {
            Logger.error(`PersonalityAssessmentSet::findById::Error finding record. Exception: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            assessmentId: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            questionId: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            userAnswer: {
                type: DataTypes.SMALLINT,
                allowNull: true
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
            },
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [];
    }

    private convertToModelAttributes(record: any): PersonalityAssessmentSetModelAttributes {
        return {
            id: record.id,
            assessmentId: record.assessmentId,
            questionId: record.questionId,
            userId: record.userId,
            userAnswer: record.userAnswer,
            createdAt: record.createdAt,
            updatedAt: record.updatedAt
        };
    }
}

