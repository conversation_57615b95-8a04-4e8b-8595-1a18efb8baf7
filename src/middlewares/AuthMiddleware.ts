import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import Logger from "../lib/Logger";

export function authenticateToken(req: Request, res: Response, next: NextFunction) {
    const authHeader: string | undefined = req.headers['authorization'];
    const token = authHeader ? authHeader.split(" ")[1] : "";
    Logger.info(`authenticateToken::Token: ${token}`);

    if (!token) {
        res.status(401).json({ message: 'Access token is missing' });
        return;
    }

    jwt.verify(token, process.env.JWT_SECRET!, (err, user) => {
        if (err) {
            res.status(403).json({ message: 'Invalid token' });
            return;
        }
        (req as any).customParamsInReq.currentUser = user; // Consider extending Request type instead of "any"
        next();
    });
}
