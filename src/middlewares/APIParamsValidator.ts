
import Logger from '../lib/Logger';
import APIParamSignature from "./APIParamSignature";
import ValidateHelper from "../lib/utils/ValidateHelper";
import GenericOpHelpler from "../lib/utils/GenericOpHelpler";
import { ApiParamSignatureInterface, APIParamValidatorInterface } from "../lib/types/Global";
import ResponseHelper from "./ResponseHelper";
import { Error, Success } from "../lib/types/Response";

/**
 * Class to validate API parameters
 *
 * @class APIParamValidator
 */
export default class APIParamValidator {
    private apiName: string;

    private paramsConfig: ApiParamSignatureInterface;

    private internalParams: Record<string, any>;

    private externalParams: Record<string, any>;

    private paramErrors: string[];

    constructor(params: any) {
        const oThis = this;
        oThis.apiName = params.apiName;
        oThis.internalParams = params.internalParams;
        oThis.externalParams = params.externalParams;

        oThis.paramErrors = [];
    }

    async perform() {
        const oThis = this;

        await oThis.fetchApiParamsConfig();

        await oThis.validateMandatoryParams();

        await oThis.checkOptionalParams();

        return oThis.prepareResponse();
    }

    private async fetchApiParamsConfig(): Promise<void> {
        const oThis = this;

        oThis.paramsConfig = APIParamSignature[oThis.apiName];

        if (!oThis.paramsConfig) {
            return oThis.unauthorizedResponse('invalidApiSignature', 's_l_v_ap_fapc_0');
        }
    }

    private validateMandatoryParams(): void {
        const oThis = this;

        const mandatoryKeys = oThis.paramsConfig.mandatory || [];

        for (let index = 0; index < mandatoryKeys.length; index++) {
            const whiteListedKeyConfig = mandatoryKeys[index];
            const whiteListedKeyName = whiteListedKeyConfig.parameter;

            const sourceParams = whiteListedKeyConfig.kind && whiteListedKeyConfig.kind == 'internal'
                ? oThis.internalParams
                : oThis.externalParams;

            if (
                Object.prototype.hasOwnProperty.call(sourceParams, whiteListedKeyName)
                && !ValidateHelper.isVarNullOrUndefined(sourceParams[whiteListedKeyName])
            ) {
                oThis.validateValue(whiteListedKeyName, whiteListedKeyConfig, sourceParams);
            } else {
                if (whiteListedKeyName === 'api_key') {
                    oThis.paramErrors.push(`invalidAPIKey`);
                } else {
                    oThis.paramErrors.push(`invalid${GenericOpHelpler.capitalizeFirstLetter(whiteListedKeyName)}`);
                }
            }
        }
    }

    private checkOptionalParams(): void {
        const oThis = this;

        const optionalKeysConfig = oThis.paramsConfig.optional || [];

        for (let index = 0; index < optionalKeysConfig.length; index++) {
            const optionalKeyConfig = optionalKeysConfig[index];
            const optionalKeyName = optionalKeyConfig.parameter;

            const sourceParams = optionalKeyConfig.kind && optionalKeyConfig.kind == 'internal'
                ? oThis.internalParams
                : oThis.externalParams;
            if (
                Object.prototype.hasOwnProperty.call(sourceParams, optionalKeyName)
                && !ValidateHelper.isVarNullOrUndefined(sourceParams[optionalKeyName])
            ) {
                oThis.validateValue(optionalKeyName, optionalKeyConfig, sourceParams);
            }
        }
    }

    validateValue(
        keyName: string,
        keyConfig: APIParamValidatorInterface,
        sourceParams: Record<string, any>,
    ): boolean {
        const oThis = this;
        const valueToValidate = sourceParams[keyName];
        const validatorMethodsArray = keyConfig.validatorMethods;

        for (let index = 0; index < validatorMethodsArray.length; index++) {
            const validatorMethodObject = validatorMethodsArray[index];
            const validatorMethodName = Object.keys(validatorMethodObject)[0];
            const validatorMethodErrorkey = validatorMethodObject[validatorMethodName];
            const validatorMethodInstance = ValidateHelper[validatorMethodName];

            let isValueValid = null;

            if (validatorMethodInstance) {
                isValueValid = validatorMethodInstance.apply(ValidateHelper, [valueToValidate]);
            } else {
                isValueValid = false;
                Logger.error(`ApiParamValidator::validateValue::${validatorMethodName} does not exist.`);
            }

            if (!isValueValid) {
                if (validatorMethodErrorkey) {
                    oThis.paramErrors.push(validatorMethodErrorkey);
                } else {
                    oThis.paramErrors.push(`invalid${GenericOpHelpler.capitalizeFirstLetter(keyName)}`);
                }

                return false;
            }
        }

        return true;
    }

    /**
     * Invalid error
     *
     * @param {string} errorCode
     *
     * @returns {Promise<void>}
     */
    private unauthorizedResponse(errorIdentifier: string, errorCode: string): Promise<void> {
        return Promise.reject(
            ResponseHelper.error([errorIdentifier], { errorCode }),
        );
    }

    /**
     * Api params validation response
     *
     * @private
     * @return {Promise<Success|Error>}
     */
    private async prepareResponse(): Promise<Success | Error> {
        const oThis = this;
        Logger.debug(`ApiParam::prepareResponse::paramErrors: ${JSON.stringify(oThis.paramErrors)}`);
        if (oThis.paramErrors.length > 0) {
            return ResponseHelper.error(oThis.paramErrors, { msg: 'Mandatory params is missing or invalid' });
        }
        return ResponseHelper.success({});
    }
}

