import { Response } from 'express';
import { Error, Success } from "../lib/types/Response";
import ErrorConfigType from "../constant/ErrorConfigTypes";
import Logger from '../lib/Logger';


class ResponseHelper {
    public success(data: any): Success {
        return {
            success: true,
            data,
        };
    }

    // Returns {success: false, errorData: {"field": "Error message with minor details"}}
    public error(errorIdentifiers: string[], errorParams: any = {}, errorCode: number = 500): Error {
        const errorData: Record<string, string> = {};
        for (let i = 0; i < errorIdentifiers.length; i++) {
            Logger.info(`ResponseHelper-error: Working on error identifier: ${errorIdentifiers[i]}`)
            const errorConfig = ErrorConfigType[errorIdentifiers[i]];
            errorData[errorConfig.parameter] = errorConfig.message;
            const parameter = errorConfig?.parameter ?? errorIdentifiers[i].toString();
            errorData[parameter] = errorConfig?.message ?? JSON.stringify(errorParams);
        }
        return {
            success: false,
            errorData,
            errorCode
        };
    }

    public renderWithStatus(formattedData: Success | Error, responseObj: Response, code = 200) {
        return responseObj.status(code).json(formattedData);
    }
}

export default new ResponseHelper();
