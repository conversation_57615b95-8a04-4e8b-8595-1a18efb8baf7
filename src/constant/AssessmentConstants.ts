/**
 * Module to define API name.
 */
class AssessmentConstants {
    public get assessmentStatusCompleted(): string {
        return "COMPLETED"
    }

    public get assessmentStatusResultDeclared(): string {
        return "RESULT_DECLARED"
    }

    public get assessmentStatusCreated(): string {
        return "CREATED"
    }

    public get assessmentStatusStarted(): string {
        return "IN_PROGRESS"
    }

    public get assessmentStatusLeft(): string {
        // Ended by user
        return "LEFT"
    }

    public get assessmentStatusTimeout(): string {
        return "TIMED_OUT"
    }

    public get assessmentStatus(): Record<string, number> {
        const classInst = this;
        return {
            [classInst.assessmentStatusCompleted]: 0,
            [classInst.assessmentStatusCreated]: 1,
            [classInst.assessmentStatusStarted]: 2,
            [classInst.assessmentStatusLeft]: 4,
            [classInst.assessmentStatusTimeout]: 5,
            [classInst.assessmentStatusResultDeclared]: 6
        }
    }

    public get assessmentStatusInvert(): Record<number, string> {
        const classInst = this;
        return {
            0: classInst.assessmentStatusCompleted,
            1: classInst.assessmentStatusCreated,
            2: classInst.assessmentStatusStarted,
            4: classInst.assessmentStatusLeft,
            5: classInst.assessmentStatusTimeout
        }
    }

    public get pendingAssessmentStatuses(): number[] {
        const classInst = this;
        return [
            classInst.assessmentStatus[classInst.assessmentStatusCreated],
            classInst.assessmentStatus[classInst.assessmentStatusStarted],
            classInst.assessmentStatus[classInst.assessmentStatusLeft]
        ];
    }

    public get personalityAssessmentType(): string {
        return "PERSONALITY_QUIZ";
    }

    public get occupationAssessmentType(): string {
        return "OCCUPATION_QUIZ";
    }

    public get personalityAssessmentStatus(): Record<string, number> {
        const classInst = this;
        return {
            [classInst.personalityAssessmentType]: 1,
            [classInst.occupationAssessmentType]: 2
        }
    }

    public get occupationAssessmentStatusInvert(): Record<number, string> {
        const classInst = this;
        return {
            1: classInst.personalityAssessmentType,
            2: classInst.occupationAssessmentType
        }
    }
}

export default new AssessmentConstants();
