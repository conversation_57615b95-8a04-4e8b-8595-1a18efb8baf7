/**
 * Module to define education-related constants
 */

// Education Types - Different categories of education records
export const EDUCATION_TYPES = {
    HIGH_SCHOOL: 1,
    GRADUATION: 2,
    POST_GRADUATION: 3,
    DIP<PERSON>OMA: 4,
    CERTIFICATION: 5,
    PROFESSIONAL_COURSE: 6,
    ONLINE_COURSE: 7,
    SKILL_DEVELOPMENT: 8
} as const;

// Education Level Names for display
export const EDUCATION_TYPE_NAMES = {
    [EDUCATION_TYPES.HIGH_SCHOOL]: 'High School',
    [EDUCATION_TYPES.GRADUATION]: 'Graduation',
    [EDUCATION_TYPES.POST_GRADUATION]: 'Post Graduation',
    [EDUCATION_TYPES.DIPLOMA]: 'Diploma',
    [EDUCATION_TYPES.CERTIFICATION]: 'Certification',
    [EDUCATION_TYPES.PROFESSIONAL_COURSE]: 'Professional Course',
    [EDUCATION_TYPES.ONLINE_COURSE]: 'Online Course',
    [EDUCATION_TYPES.SKILL_DEVELOPMENT]: 'Skill Development'
} as const;

// Degree Levels for graduation and post-graduation
export const DEGREE_LEVELS = {
    // Graduation levels
    BACHELOR_ARTS: 'B.A.',
    BACHELOR_SCIENCE: 'B.Sc.',
    BACHELOR_COMMERCE: 'B.Com.',
    BACHELOR_TECHNOLOGY: 'B.Tech',
    BACHELOR_ENGINEERING: 'B.E.',
    BACHELOR_BUSINESS_ADMIN: 'BBA',
    BACHELOR_COMPUTER_APPLICATION: 'BCA',
    BACHELOR_MEDICINE: 'MBBS',
    BACHELOR_LAW: 'LLB',
    BACHELOR_DESIGN: 'B.Des',
    BACHELOR_ARCHITECTURE: 'B.Arch',
    BACHELOR_PHARMACY: 'B.Pharm',
    
    // Post-graduation levels
    MASTER_ARTS: 'M.A.',
    MASTER_SCIENCE: 'M.Sc.',
    MASTER_COMMERCE: 'M.Com.',
    MASTER_TECHNOLOGY: 'M.Tech',
    MASTER_ENGINEERING: 'M.E.',
    MASTER_BUSINESS_ADMIN: 'MBA',
    MASTER_COMPUTER_APPLICATION: 'MCA',
    MASTER_MEDICINE: 'MD',
    MASTER_LAW: 'LLM',
    MASTER_DESIGN: 'M.Des',
    MASTER_ARCHITECTURE: 'M.Arch',
    MASTER_PHARMACY: 'M.Pharm',
    
    // Doctoral levels
    DOCTOR_PHILOSOPHY: 'Ph.D',
    DOCTOR_MEDICINE: 'DM',
    DOCTOR_SCIENCE: 'D.Sc',
    
    // Professional degrees
    CHARTERED_ACCOUNTANT: 'CA',
    COMPANY_SECRETARY: 'CS',
    COST_MANAGEMENT_ACCOUNTANT: 'CMA',
    
    // Diploma levels
    DIPLOMA_ENGINEERING: 'Diploma in Engineering',
    DIPLOMA_COMPUTER_APPLICATION: 'Diploma in Computer Application',
    DIPLOMA_MANAGEMENT: 'Diploma in Management',
    
    // Other
    OTHER: 'Other'
} as const;

// Certification Categories
export const CERTIFICATION_CATEGORIES = {
    TECHNICAL: 'Technical',
    MANAGEMENT: 'Management',
    LANGUAGE: 'Language',
    PROFESSIONAL: 'Professional',
    INDUSTRY_SPECIFIC: 'Industry Specific',
    SOFT_SKILLS: 'Soft Skills',
    DIGITAL_MARKETING: 'Digital Marketing',
    DATA_SCIENCE: 'Data Science',
    CLOUD_COMPUTING: 'Cloud Computing',
    CYBERSECURITY: 'Cybersecurity',
    PROJECT_MANAGEMENT: 'Project Management',
    FINANCE: 'Finance',
    HEALTHCARE: 'Healthcare',
    OTHER: 'Other'
} as const;

// Education Status
export const EDUCATION_STATUS = {
    COMPLETED: 1,
    IN_PROGRESS: 2,
    DROPPED: 3,
    DEFERRED: 4
} as const;

export const EDUCATION_STATUS_NAMES = {
    [EDUCATION_STATUS.COMPLETED]: 'Completed',
    [EDUCATION_STATUS.IN_PROGRESS]: 'In Progress',
    [EDUCATION_STATUS.DROPPED]: 'Dropped',
    [EDUCATION_STATUS.DEFERRED]: 'Deferred'
} as const;

// Grading Systems
export const GRADING_SYSTEMS = {
    PERCENTAGE: 'percentage',
    CGPA_10: 'cgpa_10',
    CGPA_4: 'cgpa_4',
    GPA: 'gpa',
    GRADE: 'grade',
    PASS_FAIL: 'pass_fail'
} as const;

// Grade Values for non-percentage systems
export const GRADE_VALUES = {
    'A+': 95,
    'A': 90,
    'A-': 85,
    'B+': 80,
    'B': 75,
    'B-': 70,
    'C+': 65,
    'C': 60,
    'C-': 55,
    'D': 50,
    'F': 0,
    'PASS': 60,
    'FAIL': 0
} as const;

// Type definitions for TypeScript
export type EducationType = typeof EDUCATION_TYPES[keyof typeof EDUCATION_TYPES];
export type EducationStatus = typeof EDUCATION_STATUS[keyof typeof EDUCATION_STATUS];
export type GradingSystem = typeof GRADING_SYSTEMS[keyof typeof GRADING_SYSTEMS];
export type DegreeLevel = typeof DEGREE_LEVELS[keyof typeof DEGREE_LEVELS];
export type CertificationCategory = typeof CERTIFICATION_CATEGORIES[keyof typeof CERTIFICATION_CATEGORIES];

// Helper functions
export const getEducationTypeName = (type: EducationType): string => {
    return EDUCATION_TYPE_NAMES[type] || 'Unknown';
};

export const getEducationStatusName = (status: EducationStatus): string => {
    return EDUCATION_STATUS_NAMES[status] || 'Unknown';
};

export const isValidEducationType = (type: number): type is EducationType => {
    return Object.values(EDUCATION_TYPES).includes(type as EducationType);
};

export const isValidEducationStatus = (status: number): status is EducationStatus => {
    return Object.values(EDUCATION_STATUS).includes(status as EducationStatus);
};

export const isValidGradingSystem = (system: string): system is GradingSystem => {
    return Object.values(GRADING_SYSTEMS).includes(system as GradingSystem);
};

// Validation helpers
export const validatePercentage = (percentage: number): boolean => {
    return percentage >= 0 && percentage <= 100;
};

export const validateCGPA = (cgpa: number, scale: 4 | 10): boolean => {
    return cgpa >= 0 && cgpa <= scale;
};

export const validateYear = (year: number): boolean => {
    const currentYear = new Date().getFullYear();
    return year >= 1950 && year <= currentYear + 10;
};

class EducationConstants {
    public get educationTypes() {
        return EDUCATION_TYPES;
    }

    public get educationTypeNames() {
        return EDUCATION_TYPE_NAMES;
    }

    public get degreeLevels() {
        return DEGREE_LEVELS;
    }

    public get certificationCategories() {
        return CERTIFICATION_CATEGORIES;
    }

    public get educationStatus() {
        return EDUCATION_STATUS;
    }

    public get educationStatusNames() {
        return EDUCATION_STATUS_NAMES;
    }

    public get gradingSystems() {
        return GRADING_SYSTEMS;
    }

    public get gradeValues() {
        return GRADE_VALUES;
    }

    // Helper methods
    public getEducationTypeName(type: EducationType): string {
        return getEducationTypeName(type);
    }

    public getEducationStatusName(status: EducationStatus): string {
        return getEducationStatusName(status);
    }

    public isValidEducationType(type: number): type is EducationType {
        return isValidEducationType(type);
    }

    public isValidEducationStatus(status: number): status is EducationStatus {
        return isValidEducationStatus(status);
    }

    public validatePercentage(percentage: number): boolean {
        return validatePercentage(percentage);
    }

    public validateCGPA(cgpa: number, scale: 4 | 10): boolean {
        return validateCGPA(cgpa, scale);
    }

    public validateYear(year: number): boolean {
        return validateYear(year);
    }
}

export default new EducationConstants();
