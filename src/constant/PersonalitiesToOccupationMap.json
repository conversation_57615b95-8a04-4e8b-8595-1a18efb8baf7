{"1-2-3": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}], "1-2-4": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}], "1-2-5": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}], "1-2-6": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "1-3-4": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}], "1-3-5": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}], "1-3-6": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "1-4-5": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}], "1-4-6": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "1-5-6": [{"name": "Architect", "slug": "architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Architect (Own Practice)", "slug": "architect_own_practice", "minimum_qualification": 3, "personality_id": 1}, {"name": "Clinical Engineer", "slug": "clinical_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Construction Project Mgr", "slug": "construction_project_mgr", "minimum_qualification": 3, "personality_id": 1}, {"name": "Equipment Maintenance Planner", "slug": "equipment_maintenance_planner", "minimum_qualification": 3, "personality_id": 1}, {"name": "Exhibit <PERSON>nician", "slug": "exhibit_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Landscape Architect", "slug": "landscape_architect", "minimum_qualification": 3, "personality_id": 1}, {"name": "Manufacturing Process Eng", "slug": "manufacturing_process_eng", "minimum_qualification": 3, "personality_id": 1}, {"name": "Petroleum Engineer (Mgmt)", "slug": "petroleum_engineer_mgmt", "minimum_qualification": 3, "personality_id": 1}, {"name": "Rehabilitation Engineer", "slug": "rehabilitation_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Safety Engineer", "slug": "safety_engineer", "minimum_qualification": 3, "personality_id": 1}, {"name": "Sound Technician", "slug": "sound_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Veterinary Technician", "slug": "veterinary_technician", "minimum_qualification": 3, "personality_id": 1}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "2-3-4": [{"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}], "2-3-5": [{"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}], "2-3-6": [{"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "2-4-5": [{"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}], "2-4-6": [{"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "2-5-6": [{"name": "Actuarial Consultant", "slug": "actuarial_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Agronomist", "slug": "agronomist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Biomedical Equipment Tech", "slug": "biomedical_equipment_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Building Inspector (Detailed)", "slug": "building_inspector_detailed", "minimum_qualification": 3, "personality_id": 2}, {"name": "Clinical Research Coord", "slug": "clinical_research_coord", "minimum_qualification": 3, "personality_id": 2}, {"name": "Computer Forensics Spec", "slug": "computer_forensics_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "Data Visualization Spec", "slug": "data_visualization_spec", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "dietitian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Energy Consultant", "slug": "energy_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Environmental Consultant", "slug": "environmental_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Epidemiologist", "slug": "epidemiologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Advisor", "slug": "financial_advisor", "minimum_qualification": 3, "personality_id": 2}, {"name": "Financial Aid Officer", "slug": "financial_aid_officer", "minimum_qualification": 3, "personality_id": 2}, {"name": "Food Safety Inspector", "slug": "food_safety_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "Forensic Accountant", "slug": "forensic_accountant", "minimum_qualification": 2, "personality_id": 2}, {"name": "Forensic Anthropologist", "slug": "forensic_anthropologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Fundraiser (Physical)", "slug": "fundraiser_physical", "minimum_qualification": 3, "personality_id": 2}, {"name": "Geospatial Analyst", "slug": "geospatial_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Health Information Mgr", "slug": "health_information_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "It Consultant", "slug": "it_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON> (Research)", "slug": "librarian_research", "minimum_qualification": 3, "personality_id": 2}, {"name": "Logistics Analyst", "slug": "logistics_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Management Consultant", "slug": "management_consultant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst", "slug": "market_research_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Analyst (Quant)", "slug": "market_research_analyst_quant", "minimum_qualification": 3, "personality_id": 2}, {"name": "Market Research Director", "slug": "market_research_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Coder", "slug": "medical_coder", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Records Admin", "slug": "medical_records_admin", "minimum_qualification": 3, "personality_id": 2}, {"name": "Medical Technologist", "slug": "medical_technologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Non-Profit Director", "slug": "non_profit_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "Organizational Dev Cons", "slug": "organizational_dev_cons", "minimum_qualification": 3, "personality_id": 2}, {"name": "Paralegal", "slug": "paralegal", "minimum_qualification": 3, "personality_id": 2}, {"name": "Park Ranger", "slug": "park_ranger", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmaceutical QA", "slug": "pharmaceutical_qa", "minimum_qualification": 3, "personality_id": 2}, {"name": "Pharmacy Technician", "slug": "pharmacy_technician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Policy Analyst", "slug": "policy_analyst", "minimum_qualification": 3, "personality_id": 2}, {"name": "Prosthetist/Orthotist", "slug": "prosthetist_orthotist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Public Health Inspector", "slug": "public_health_inspector", "minimum_qualification": 3, "personality_id": 2}, {"name": "R&D Director", "slug": "r_d_director", "minimum_qualification": 3, "personality_id": 2}, {"name": "<PERSON><PERSON><PERSON><PERSON> (Executive)", "slug": "recruiter_executive", "minimum_qualification": 3, "personality_id": 2}, {"name": "Sports Medicine Physician", "slug": "sports_medicine_physician", "minimum_qualification": 3, "personality_id": 2}, {"name": "Strategic Consultant (Tech)", "slug": "strategic_consultant_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Supply Chain Analyst", "slug": "supply_chain_analyst", "minimum_qualification": 2, "personality_id": 2}, {"name": "Surveyor (Data)", "slug": "surveyor_data", "minimum_qualification": 3, "personality_id": 2}, {"name": "Systems Integration Mgr", "slug": "systems_integration_mgr", "minimum_qualification": 3, "personality_id": 2}, {"name": "Venture Capitalist (Tech)", "slug": "venture_capitalist_tech", "minimum_qualification": 3, "personality_id": 2}, {"name": "Veterinarian", "slug": "veterinarian", "minimum_qualification": 3, "personality_id": 2}, {"name": "Wildlife Biologist", "slug": "wildlife_biologist", "minimum_qualification": 3, "personality_id": 2}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "3-4-5": [{"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}], "3-4-6": [{"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "3-5-6": [{"name": "Academic Advisor", "slug": "academic_advisor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Art Director", "slug": "advertising_art_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "Advertising Copywriter", "slug": "advertising_copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Advertising Executive", "slug": "advertising_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Animator", "slug": "animator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Td)", "slug": "animator_td", "minimum_qualification": 1, "personality_id": 3}, {"name": "Animator (Technical)", "slug": "animator_technical", "minimum_qualification": 1, "personality_id": 3}, {"name": "Architectural Drafter", "slug": "architectural_drafter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Firm Owner", "slug": "architectural_firm_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Architectural Renderer", "slug": "architectural_renderer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Archivist", "slug": "archivist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Art Administrator", "slug": "art_administrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Director (Advertising)", "slug": "art_director_advertising", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Gallery Director", "slug": "art_gallery_director", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON>", "slug": "art_restorer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Teacher", "slug": "art_teacher", "minimum_qualification": 1, "personality_id": 3}, {"name": "Art Therapist", "slug": "art_therapist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Artist Manager", "slug": "artist_manager", "minimum_qualification": 1, "personality_id": 3}, {"name": "Automotive Designer", "slug": "automotive_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Book Conservator", "slug": "book_conservator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Book Designer", "slug": "book_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Brand Manager (Creative)", "slug": "brand_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Brand Strategist", "slug": "brand_strategist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Cad Designer", "slug": "cad_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Chef/Culinary Instructor", "slug": "chef_culinary_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Commercial Artist", "slug": "commercial_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Communications Director", "slug": "communications_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Community Arts Coord", "slug": "community_arts_coord", "minimum_qualification": 1, "personality_id": 3}, {"name": "Community Arts Organizer", "slug": "community_arts_organizer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Computer Graphics Artist", "slug": "computer_graphics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Content Creator", "slug": "content_creator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Copy<PERSON>", "slug": "copywriter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Costume Designer", "slug": "costume_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Costume Fabricator", "slug": "costume_fabricator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Craft Instructor", "slug": "craft_instructor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Consultant", "slug": "creative_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director", "slug": "creative_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Director (Agency)", "slug": "creative_director_agency", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Engineer", "slug": "creative_engineer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Creative Marketing Mgr", "slug": "creative_marketing_mgr", "minimum_qualification": 2, "personality_id": 3}, {"name": "Creative Project Manager", "slug": "creative_project_manager", "minimum_qualification": 2, "personality_id": 3}, {"name": "Curator", "slug": "curator", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Art)", "slug": "curator_art", "minimum_qualification": 1, "personality_id": 3}, {"name": "Customer Service Rep", "slug": "customer_service_rep", "minimum_qualification": 3, "personality_id": 3}, {"name": "Desktop Publisher", "slug": "desktop_publisher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Display Artist", "slug": "display_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Documentary Filmmaker", "slug": "documentary_filmmaker", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON>reneur (Creative Bus)", "slug": "entrepreneur_creative_bus", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON>ur (Creative Svcs)", "slug": "entrepreneur_creative_svcs", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Tech/Creative)", "slug": "entrepreneur_tech_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Environmental Educator", "slug": "environmental_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Event Decorator", "slug": "event_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer", "slug": "exhibit_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Interactive)", "slug": "exhibit_designer_interactive", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Designer (Sales)", "slug": "exhibit_designer_sales", "minimum_qualification": 1, "personality_id": 3}, {"name": "Exhibit Preparator", "slug": "exhibit_preparator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Fashion Buyer", "slug": "fashion_buyer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Designer (Label)", "slug": "fashion_designer_label", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Merchandiser", "slug": "fashion_merchandiser", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Production Asst", "slug": "fashion_production_asst", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fashion Stylist", "slug": "fashion_stylist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Film Director", "slug": "film_director", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Post-Prod)", "slug": "film_editor_post_prod", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Editor (Technical)", "slug": "film_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Film Producer", "slug": "film_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Forensic Artist", "slug": "forensic_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Forensic Photographer", "slug": "forensic_photographer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Fundraiser (Arts)", "slug": "fundraiser_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Gallery Owner", "slug": "gallery_owner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Game Designer", "slug": "game_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON> Writer", "slug": "grant_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Graphic Designer", "slug": "graphic_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Graphic Designer (Print)", "slug": "graphic_designer_print", "minimum_qualification": 1, "personality_id": 3}, {"name": "Hair Stylist/Makeup Artist", "slug": "hair_stylist_makeup_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Industrial Designer", "slug": "industrial_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Innovation Consultant", "slug": "innovation_consultant", "minimum_qualification": 3, "personality_id": 3}, {"name": "Interior Designer", "slug": "interior_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Interior Designer (Biz)", "slug": "interior_designer_biz", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON> (Creative)", "slug": "ip_attorney_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jeweler (Custom)", "slug": "jeweler_custom", "minimum_qualification": 3, "personality_id": 3}, {"name": "Jewelry Designer", "slug": "jewelry_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Journalist (Investigative)", "slug": "journalist_investigative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Landscape Designer", "slug": "landscape_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Librarian", "slug": "librarian", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Children'S)", "slug": "librarian_children_s", "minimum_qualification": 3, "personality_id": 3}, {"name": "<PERSON><PERSON><PERSON> (Special)", "slug": "librarian_special", "minimum_qualification": 3, "personality_id": 3}, {"name": "Lighting Designer", "slug": "lighting_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Marketing Director (Data/Creative)", "slug": "marketing_director_data_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Medical Device Designer", "slug": "medical_device_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Medical Illustrator", "slug": "medical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Museum Collections Mgr", "slug": "museum_collections_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Museum Educator", "slug": "museum_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Industry Executive", "slug": "music_industry_executive", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Producer", "slug": "music_producer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Music Promoter", "slug": "music_promoter", "minimum_qualification": 3, "personality_id": 3}, {"name": "Pattern Maker", "slug": "pattern_maker", "minimum_qualification": 3, "personality_id": 3}, {"name": "Photographer (Commercial)", "slug": "photographer_commercial", "minimum_qualification": 1, "personality_id": 3}, {"name": "Photography Instructor", "slug": "photography_instructor", "minimum_qualification": 1, "personality_id": 3}, {"name": "Product Designer", "slug": "product_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Prop Master", "slug": "prop_master", "minimum_qualification": 3, "personality_id": 3}, {"name": "Public Health Educator", "slug": "public_health_educator", "minimum_qualification": 3, "personality_id": 3}, {"name": "R&D Manager (Creative)", "slug": "r_d_manager_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Restaurant Owner/Chef", "slug": "restaurant_owner_chef", "minimum_qualification": 3, "personality_id": 3}, {"name": "Restaurant Owner/Mgr", "slug": "restaurant_owner_mgr", "minimum_qualification": 3, "personality_id": 3}, {"name": "Robotics Artist", "slug": "robotics_artist", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sales Rep (Creative)", "slug": "sales_rep_creative", "minimum_qualification": 2, "personality_id": 3}, {"name": "Science Journalist", "slug": "science_journalist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Scientific Illustrator", "slug": "scientific_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Scientific Instrument Designer", "slug": "scientific_instrument_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sculp<PERSON>", "slug": "sculptor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Builder", "slug": "set_builder", "minimum_qualification": 3, "personality_id": 3}, {"name": "Set Decorator", "slug": "set_decorator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Set Designer", "slug": "set_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Sociologist", "slug": "sociologist", "minimum_qualification": 3, "personality_id": 3}, {"name": "Sound Designer", "slug": "sound_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Special Effects Tech", "slug": "special_effects_tech", "minimum_qualification": 3, "personality_id": 3}, {"name": "Strategic Consultant (Innovation)", "slug": "strategic_consultant_innovation", "minimum_qualification": 3, "personality_id": 3}, {"name": "Talent Agent", "slug": "talent_agent", "minimum_qualification": 3, "personality_id": 3}, {"name": "Technical Illustrator", "slug": "technical_illustrator", "minimum_qualification": 1, "personality_id": 3}, {"name": "Technical Writer", "slug": "technical_writer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Therapist (Arts)", "slug": "therapist_arts", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ui Designer", "slug": "ui_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "University Professor (Humanities)", "slug": "university_professor_humanities", "minimum_qualification": 3, "personality_id": 3}, {"name": "Urban Planner", "slug": "urban_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Ux Designer", "slug": "ux_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Ux Researcher", "slug": "ux_researcher", "minimum_qualification": 3, "personality_id": 3}, {"name": "Venture Capitalist (Creative)", "slug": "venture_capitalist_creative", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor", "slug": "video_editor", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Editor (Technical)", "slug": "video_editor_technical", "minimum_qualification": 3, "personality_id": 3}, {"name": "Video Game Developer", "slug": "video_game_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Visual Effects Supervisor", "slug": "visual_effects_supervisor", "minimum_qualification": 1, "personality_id": 3}, {"name": "<PERSON><PERSON>", "slug": "vr_developer", "minimum_qualification": 3, "personality_id": 3}, {"name": "Web Designer", "slug": "web_designer", "minimum_qualification": 1, "personality_id": 3}, {"name": "Web Designer (Code)", "slug": "web_designer_code", "minimum_qualification": 1, "personality_id": 3}, {"name": "Wedding Planner", "slug": "wedding_planner", "minimum_qualification": 3, "personality_id": 3}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}], "4-5-6": [{"name": "Audiologist", "slug": "audiologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Automotive Service Mgr", "slug": "automotive_service_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Budget Analyst (Social)", "slug": "budget_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Childcare Worker", "slug": "childcare_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Client Relationship Mgr (Tech)", "slug": "client_relationship_mgr_tech", "minimum_qualification": 3, "personality_id": 4}, {"name": "Corrections Officer", "slug": "corrections_officer", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor", "slug": "counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Counselor (Records)", "slug": "counselor_records", "minimum_qualification": 3, "personality_id": 4}, {"name": "Data Analyst (Social)", "slug": "data_analyst_social", "minimum_qualification": 3, "personality_id": 4}, {"name": "Dental Hygienist", "slug": "dental_hygienist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Drama Teacher", "slug": "drama_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Emt", "slug": "emt", "minimum_qualification": 3, "personality_id": 4}, {"name": "Firefighter", "slug": "firefighter", "minimum_qualification": 3, "personality_id": 4}, {"name": "Fitness Coach", "slug": "fitness_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Franchise Owner (Service)", "slug": "franchise_owner_service", "minimum_qualification": 3, "personality_id": 4}, {"name": "Genetic Counselor", "slug": "genetic_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "Health And Safety Mgr", "slug": "health_and_safety_mgr", "minimum_qualification": 3, "personality_id": 4}, {"name": "Home Health Aide", "slug": "home_health_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Horticultural Therapist", "slug": "horticultural_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "HR Manager", "slug": "hr_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "HR Specialist (Compliance)", "slug": "hr_specialist_compliance", "minimum_qualification": 2, "personality_id": 4}, {"name": "Music Teacher", "slug": "music_teacher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Music Therapist", "slug": "music_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapist", "slug": "occupational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Occupational Therapy Assistant", "slug": "occupational_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Paramedic", "slug": "paramedic", "minimum_qualification": 3, "personality_id": 4}, {"name": "Patient Care Technician", "slug": "patient_care_technician", "minimum_qualification": 3, "personality_id": 4}, {"name": "Personal Care Aide", "slug": "personal_care_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Phlebotomist", "slug": "phlebotomist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapist (Research)", "slug": "physical_therapist_research", "minimum_qualification": 3, "personality_id": 4}, {"name": "Physical Therapy Assistant", "slug": "physical_therapy_assistant", "minimum_qualification": 3, "personality_id": 4}, {"name": "Psychologist (Clinical/Res)", "slug": "psychologist_clinical_res", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Aide", "slug": "recreational_aide", "minimum_qualification": 3, "personality_id": 4}, {"name": "Recreational Therapist", "slug": "recreational_therapist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Rec<PERSON>er (Trades)", "slug": "recruiter_trades", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Counselor", "slug": "school_counselor", "minimum_qualification": 3, "personality_id": 4}, {"name": "School Psychologist", "slug": "school_psychologist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Security Guard", "slug": "security_guard", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Entrepreneur", "slug": "social_entrepreneur", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Coord", "slug": "social_media_coord", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Media Manager", "slug": "social_media_manager", "minimum_qualification": 2, "personality_id": 4}, {"name": "Social Media Strategist", "slug": "social_media_strategist", "minimum_qualification": 3, "personality_id": 4}, {"name": "Social Science Researcher", "slug": "social_science_researcher", "minimum_qualification": 3, "personality_id": 4}, {"name": "Sports Coach", "slug": "sports_coach", "minimum_qualification": 3, "personality_id": 4}, {"name": "Statistician (Social Sci)", "slug": "statistician_social_sci", "minimum_qualification": 3, "personality_id": 4}, {"name": "Support Worker", "slug": "support_worker", "minimum_qualification": 3, "personality_id": 4}, {"name": "Tour Operator", "slug": "tour_operator", "minimum_qualification": 3, "personality_id": 4}, {"name": "Vocational School Admin", "slug": "vocational_school_admin", "minimum_qualification": 3, "personality_id": 4}, {"name": "Youth Program Director", "slug": "youth_program_director", "minimum_qualification": 3, "personality_id": 4}, {"name": "Aerospace Eng Manager", "slug": "aerospace_eng_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Biotech Entrepreneur", "slug": "biotech_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Biotechnology Entrepreneur", "slug": "biotechnology_entrepreneur", "minimum_qualification": 3, "personality_id": 5}, {"name": "Brand Manager", "slug": "brand_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Business Development Mgr", "slug": "business_development_mgr", "minimum_qualification": 2, "personality_id": 5}, {"name": "Clinical Data Manager", "slug": "clinical_data_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Construction Manager", "slug": "construction_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Dietary Manager", "slug": "dietary_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Asset Manager", "slug": "digital_asset_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Spec", "slug": "digital_marketing_spec", "minimum_qualification": 2, "personality_id": 5}, {"name": "Digital Marketing Strat", "slug": "digital_marketing_strat", "minimum_qualification": 2, "personality_id": 5}, {"name": "Engineering Manager", "slug": "engineering_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Entrepreneur (Mfg/Tech)", "slug": "entrepreneur_mfg_tech", "minimum_qualification": 3, "personality_id": 5}, {"name": "Entrepreneur (Service/Res)", "slug": "entrepreneur_service_res", "minimum_qualification": 3, "personality_id": 5}, {"name": "Event Manager", "slug": "event_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Event Manager (Physical)", "slug": "event_manager_physical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Fitness Club Manager", "slug": "fitness_club_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Game Development Director", "slug": "game_development_director", "minimum_qualification": 3, "personality_id": 5}, {"name": "Hotel Manager", "slug": "hotel_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Jewelry Business Owner", "slug": "jewelry_business_owner", "minimum_qualification": 2, "personality_id": 5}, {"name": "Laboratory Manager", "slug": "laboratory_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Logistics Manager", "slug": "logistics_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Coordinator", "slug": "marketing_coordinator", "minimum_qualification": 2, "personality_id": 5}, {"name": "Marketing Director", "slug": "marketing_director", "minimum_qualification": 2, "personality_id": 5}, {"name": "Medical Device Sales", "slug": "medical_device_sales", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Analyst", "slug": "operations_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Operations Research Analyst", "slug": "operations_research_analyst", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (High-Tech)", "slug": "product_manager_high_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Product Manager (Tech/Design)", "slug": "product_manager_tech_design", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Complex)", "slug": "project_manager_complex", "minimum_qualification": 2, "personality_id": 5}, {"name": "Project Manager (Technical)", "slug": "project_manager_technical", "minimum_qualification": 2, "personality_id": 5}, {"name": "Property Manager", "slug": "property_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Coord", "slug": "public_relations_coord", "minimum_qualification": 3, "personality_id": 5}, {"name": "Public Relations Manager", "slug": "public_relations_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Public Relations Spec", "slug": "public_relations_spec", "minimum_qualification": 3, "personality_id": 5}, {"name": "Quality Assurance Manager", "slug": "quality_assurance_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Quality Control Manager", "slug": "quality_control_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Restaurant Manager", "slug": "restaurant_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Retail Manager", "slug": "retail_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Manager", "slug": "sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Sales Rep (Tech)", "slug": "sales_rep_tech", "minimum_qualification": 2, "personality_id": 5}, {"name": "Salon/Spa Manager", "slug": "salon_spa_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Security Manager", "slug": "security_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Supply Chain Manager", "slug": "supply_chain_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Technical Sales Manager", "slug": "technical_sales_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Training Manager", "slug": "training_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Ux/Ui Manager", "slug": "ux_ui_manager", "minimum_qualification": 2, "personality_id": 5}, {"name": "Visual Merchandiser", "slug": "visual_merchandiser", "minimum_qualification": 1, "personality_id": 5}, {"name": "Advertising Account Exec", "slug": "advertising_account_exec", "minimum_qualification": 2, "personality_id": 6}, {"name": "Auditor (Non-Profit)", "slug": "auditor_non_profit", "minimum_qualification": 2, "personality_id": 6}, {"name": "Compliance Officer (Healthcare)", "slug": "compliance_officer_healthcare", "minimum_qualification": 3, "personality_id": 6}, {"name": "Court Clerk", "slug": "court_clerk", "minimum_qualification": 3, "personality_id": 6}, {"name": "Database Administrator", "slug": "database_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Editorial Assistant", "slug": "editorial_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Environmental Compliance Insp", "slug": "environmental_compliance_insp", "minimum_qualification": 3, "personality_id": 6}, {"name": "Event Coordinator", "slug": "event_coordinator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Graphic Design Assistant", "slug": "graphic_design_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Healthcare Administrator", "slug": "healthcare_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "It Auditor", "slug": "it_auditor", "minimum_qualification": 2, "personality_id": 6}, {"name": "Medical Assistant", "slug": "medical_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "Network Administrator", "slug": "network_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Nursing Assistant", "slug": "nursing_assistant", "minimum_qualification": 3, "personality_id": 6}, {"name": "University Administrator", "slug": "university_administrator", "minimum_qualification": 2, "personality_id": 6}, {"name": "Youth Program Assistant", "slug": "youth_program_assistant", "minimum_qualification": 3, "personality_id": 6}]}