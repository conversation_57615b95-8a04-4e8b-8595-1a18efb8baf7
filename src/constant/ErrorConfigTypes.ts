import { ErrorConfig } from "../lib/types/Global";


/**
 * Module to define error for backend API.
 */
const ErrorConfigType: Record<string, ErrorConfig> = {
    internalServerError: {
        parameter: 'internalServer',
        message: 'There is an issue while serving request, please contact support team.',
    },
    invalidName: {
        parameter: 'invalidName',
        message: 'Please provide required parameters.',
    },
    invalidEmail: {
        parameter: 'invalidEmail',
        message: 'Please provide required parameters.',
    },
    invalidPassword: {
        parameter: 'invalidPassword',
        message: 'Please provide required parameters.',
    },
    invalidUsername: {
        parameter: 'invalidUsername',
        message: 'Please provide required parameters.',
    },
    invalidUser: {
        parameter: 'invalidUser',
        message: 'User doesn\'t exist by provided username or email.',
    },
    invalidAssessmentId: {
        parameter: 'invalidAssessmentId',
        message: 'Assessment id provided, does not exists.',
    },
    invalidAnswer: {
        parameter: 'invalidAnswer',
        message: 'Answers details not provided correctly.',
    },
    assessmentCreationFailed: {
        parameter: 'assessmentCreationFailed',
        message: 'Failed to create assessment.',
    },
    invalidAnswers: {
        parameter: 'invalidAnswers',
        message: 'Please provide valid answers array.',
    },
    invalidAnswerFormat: {
        parameter: 'invalidAnswerFormat',
        message: 'Each answer must contain question_assessment_set_id and user_answer.',
    },
    assessmentSetNotFound: {
        parameter: 'assessmentSetNotFound',
        message: 'Assessment set not found for the provided ID.',
    },
    updateFailed: {
        parameter: 'updateFailed',
        message: 'Failed to update assessment answers.',
    },
    noAssessmentResult: {
        parameter: 'noAssessmentResult',
        message: 'No assessment results found for the user.',
    },
    generalError: {
        parameter: 'generalError',
        message: 'Something went wrong.',
    },

};

export default ErrorConfigType;
