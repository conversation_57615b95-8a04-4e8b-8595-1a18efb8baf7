import express, { Request, Response, NextFunction } from 'express';
import apiRoutes from './routes/api/index';
import Database from "./db/DBConnect";
import Logger from "./lib/Logger";

const addCustomAttribute = (req: Request, res: Response, next: NextFunction) => {
    req.customParamsInReq = {};
    next();
};

const app = express();

app.use(addCustomAttribute);

// Middleware for parsing JSON
app.use(express.json());
Logger.info("Starting SoulThread server...")
app.use('/api', apiRoutes);


const startServer = async () => {
    try {
        await Database.connect();

        let port = process.env.PORT || 3000
        app.listen(port, () => {
            Logger.info(`🚀 Server running on port ${port}`);
        });

        // Handle graceful shutdown
        process.on("SIGINT", async () => {
            Logger.info("\n🛑 Closing database connection...");
            await Database.disconnect();
            process.exit(0);
        });

    } catch (error) {
        console.error("❌ Failed to start server:", error);
        process.exit(1);
    }
};

startServer().then(r => Logger.info("Waiting..."));

export default app;
