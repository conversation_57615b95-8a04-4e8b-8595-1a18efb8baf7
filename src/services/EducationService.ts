import { UserEducationModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import ResponseHelper from "../middlewares/ResponseHelper";
import PostgresModel from "../db/PostgresModel";
import Logger from "../lib/Logger";

export interface EducationUpdateData {
    highSchoolStreamId?: number;
    tenthPercentage?: number;
    eleventhPercentage?: number;
    twelfthPercentage?: number;
    // Future graduation fields
    graduationDegree?: string;
    graduationSpecialization?: string;
    graduationCollege?: string;
    graduationPercentage?: number;
    graduationStartYear?: number;
    graduationEndYear?: number;
    // Future post graduation fields
    postGraduationDegree?: string;
    postGraduationSpecialization?: string;
    postGraduationCollege?: string;
    postGraduationPercentage?: number;
    postGraduationStartYear?: number;
    postGraduationEndYear?: number;
}

export default class EducationService {
    private userId: number;
    private educationData: EducationUpdateData;

    constructor(userId: number, educationData: EducationUpdateData) {
        this.userId = userId;
        this.educationData = educationData;
    }

    public async updateUserEducation(): Promise<Success | Error> {
        try {
            await this.validateEducationData();
            await this.saveEducationData();
            
            return ResponseHelper.success({
                message: "Education details updated successfully",
                updatedFields: this.getUpdatedFieldsInfo()
            });
        } catch (error: any) {
            Logger.error(`EducationService::updateUserEducation::Error: ${error.message}`);
            return ResponseHelper.error([error.message], { code: '400' });
        }
    }

    private async validateEducationData(): Promise<void> {
        const classInst = this;

        // Validate percentage values
        const percentageFields = [
            { field: 'tenthPercentage', name: '10th percentage' },
            { field: 'eleventhPercentage', name: '11th percentage' },
            { field: 'twelfthPercentage', name: '12th percentage' },
            { field: 'graduationPercentage', name: 'graduation percentage' },
            { field: 'postGraduationPercentage', name: 'post graduation percentage' }
        ];

        for (const { field, name } of percentageFields) {
            const value = classInst.educationData[field as keyof EducationUpdateData] as number;
            if (value !== undefined && value !== null) {
                if (typeof value !== 'number' || value < 0 || value > 100) {
                    throw new Error(`${name} must be a number between 0 and 100`);
                }
            }
        }

        // Validate high school stream ID if provided
        if (classInst.educationData.highSchoolStreamId !== undefined && classInst.educationData.highSchoolStreamId !== null) {
            if (!Number.isInteger(classInst.educationData.highSchoolStreamId) || classInst.educationData.highSchoolStreamId <= 0) {
                throw new Error('High school stream ID must be a positive integer');
            }
            
            // Validate that the stream exists in education_streams table
            await classInst.validateEducationStreamExists(classInst.educationData.highSchoolStreamId);
        }

        // Validate year fields
        const currentYear = new Date().getFullYear();
        const yearFields = [
            { field: 'graduationStartYear', name: 'graduation start year' },
            { field: 'graduationEndYear', name: 'graduation end year' },
            { field: 'postGraduationStartYear', name: 'post graduation start year' },
            { field: 'postGraduationEndYear', name: 'post graduation end year' }
        ];

        for (const { field, name } of yearFields) {
            const value = classInst.educationData[field as keyof EducationUpdateData] as number;
            if (value !== undefined && value !== null) {
                if (!Number.isInteger(value) || value < 1950 || value > currentYear + 10) {
                    throw new Error(`${name} must be a valid year between 1950 and ${currentYear + 10}`);
                }
            }
        }

        // Validate graduation year logic
        if (classInst.educationData.graduationStartYear && classInst.educationData.graduationEndYear) {
            if (classInst.educationData.graduationStartYear >= classInst.educationData.graduationEndYear) {
                throw new Error('Graduation end year must be after start year');
            }
        }

        // Validate post graduation year logic
        if (classInst.educationData.postGraduationStartYear && classInst.educationData.postGraduationEndYear) {
            if (classInst.educationData.postGraduationStartYear >= classInst.educationData.postGraduationEndYear) {
                throw new Error('Post graduation end year must be after start year');
            }
        }

        Logger.debug(`EducationService::validateEducationData::Validation completed for user ${classInst.userId}`);
    }

    private async validateEducationStreamExists(streamId: number): Promise<void> {
        try {
            // This would need to be implemented when we have the EducationStream model
            // For now, we'll assume the validation is done at the database level via foreign key constraint
            Logger.debug(`EducationService::validateEducationStreamExists::Validating stream ID ${streamId}`);
        } catch (error: any) {
            Logger.error(`EducationService::validateEducationStreamExists::Error: ${error.message}`);
            throw new Error('Invalid high school stream ID');
        }
    }

    private async saveEducationData(): Promise<void> {
        try {
            const dbModels = await PostgresModel.getDbModels();
            
            // Filter out undefined values to only update provided fields
            const filteredEducationData: Partial<UserEducationModelAttributes> = {};
            
            Object.keys(this.educationData).forEach(key => {
                const value = this.educationData[key as keyof EducationUpdateData];
                if (value !== undefined && value !== null) {
                    filteredEducationData[key as keyof UserEducationModelAttributes] = value;
                }
            });

            if (Object.keys(filteredEducationData).length > 0) {
                await dbModels.userEducation.upsertUserEducation(this.userId, filteredEducationData);
                Logger.debug(`EducationService::saveEducationData::Successfully saved education data for user ${this.userId}`);
            } else {
                Logger.debug(`EducationService::saveEducationData::No education data to save for user ${this.userId}`);
            }
        } catch (error: any) {
            Logger.error(`EducationService::saveEducationData::Error saving education data: ${error.message}`);
            throw error;
        }
    }

    private getUpdatedFieldsInfo(): Record<string, boolean> {
        const updatedFields: Record<string, boolean> = {};
        
        Object.keys(this.educationData).forEach(key => {
            const value = this.educationData[key as keyof EducationUpdateData];
            updatedFields[key] = value !== undefined && value !== null;
        });

        return updatedFields;
    }

    public static async getUserEducation(userId: number): Promise<UserEducationModelAttributes | null> {
        try {
            const dbModels = await PostgresModel.getDbModels();
            return await dbModels.userEducation.getUserEducation(userId);
        } catch (error: any) {
            Logger.error(`EducationService::getUserEducation::Error: ${error.message}`);
            throw error;
        }
    }

    public static validateEducationFields(educationData: any): EducationUpdateData {
        const validFields: EducationUpdateData = {};

        // High school fields
        if (educationData.highSchoolStreamId !== undefined) {
            validFields.highSchoolStreamId = educationData.highSchoolStreamId;
        }
        if (educationData.tenthPercentage !== undefined) {
            validFields.tenthPercentage = educationData.tenthPercentage;
        }
        if (educationData.eleventhPercentage !== undefined) {
            validFields.eleventhPercentage = educationData.eleventhPercentage;
        }
        if (educationData.twelfthPercentage !== undefined) {
            validFields.twelfthPercentage = educationData.twelfthPercentage;
        }

        // Graduation fields (for future use)
        if (educationData.graduationDegree !== undefined) {
            validFields.graduationDegree = educationData.graduationDegree;
        }
        if (educationData.graduationSpecialization !== undefined) {
            validFields.graduationSpecialization = educationData.graduationSpecialization;
        }
        if (educationData.graduationCollege !== undefined) {
            validFields.graduationCollege = educationData.graduationCollege;
        }
        if (educationData.graduationPercentage !== undefined) {
            validFields.graduationPercentage = educationData.graduationPercentage;
        }
        if (educationData.graduationStartYear !== undefined) {
            validFields.graduationStartYear = educationData.graduationStartYear;
        }
        if (educationData.graduationEndYear !== undefined) {
            validFields.graduationEndYear = educationData.graduationEndYear;
        }

        // Post graduation fields (for future use)
        if (educationData.postGraduationDegree !== undefined) {
            validFields.postGraduationDegree = educationData.postGraduationDegree;
        }
        if (educationData.postGraduationSpecialization !== undefined) {
            validFields.postGraduationSpecialization = educationData.postGraduationSpecialization;
        }
        if (educationData.postGraduationCollege !== undefined) {
            validFields.postGraduationCollege = educationData.postGraduationCollege;
        }
        if (educationData.postGraduationPercentage !== undefined) {
            validFields.postGraduationPercentage = educationData.postGraduationPercentage;
        }
        if (educationData.postGraduationStartYear !== undefined) {
            validFields.postGraduationStartYear = educationData.postGraduationStartYear;
        }
        if (educationData.postGraduationEndYear !== undefined) {
            validFields.postGraduationEndYear = educationData.postGraduationEndYear;
        }

        return validFields;
    }
}
