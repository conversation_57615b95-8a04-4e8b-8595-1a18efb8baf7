import { UserEducationModelAttributes } from "../lib/types/Model";
import { Error, Success } from "../lib/types/Response";
import ResponseHelper from "../middlewares/ResponseHelper";
import PostgresModel from "../db/PostgresModel";
import Logger from "../lib/Logger";
import {
    EDUCATION_TYPES,
    EducationType,
    isValidEducationType,
    validatePercentage,
    validateYear,
    getEducationTypeName
} from "../constant/EducationConstants";

export interface EducationRecord {
    id?: number; // For updates
    educationType: number;
    institutionName?: string;
    degreeName?: string;
    specialization?: string;
    educationStreamId?: number; // For high school
    percentage?: number;
    grade?: string;
    startYear?: number;
    endYear?: number;
    isCompleted?: boolean;
    boardUniversity?: string;
    location?: string;
    certificationAuthority?: string;
    certificateNumber?: string;
    expiryDate?: Date;
    notes?: string;
}

export default class EducationService {
    private userId: number;
    private educationRecords: EducationRecord[];

    constructor(userId: number, educationRecords: EducationRecord[]) {
        this.userId = userId;
        this.educationRecords = educationRecords;
    }

    public async updateUserEducation(): Promise<Success | Error> {
        try {
            await this.validateEducationRecords();
            await this.saveEducationRecords();

            return ResponseHelper.success({
                message: "Education details updated successfully",
                recordsProcessed: this.educationRecords.length,
                educationTypes: this.educationRecords.map(record => getEducationTypeName(record.educationType))
            });
        } catch (error: any) {
            Logger.error(`EducationService::updateUserEducation::Error: ${error.message}`);
            return ResponseHelper.error([error.message], { code: '400' });
        }
    }

    private async validateEducationRecords(): Promise<void> {
        const classInst = this;

        if (!classInst.educationRecords || classInst.educationRecords.length === 0) {
            throw new Error('At least one education record must be provided');
        }

        // Check for duplicate high school records
        const highSchoolRecords = classInst.educationRecords.filter(record =>
            record.educationType === EDUCATION_TYPES.HIGH_SCHOOL
        );

        if (highSchoolRecords.length > 1) {
            throw new Error('Only one high school record is allowed per user');
        }

        // Validate each education record
        for (let i = 0; i < classInst.educationRecords.length; i++) {
            const record = classInst.educationRecords[i];
            await classInst.validateSingleEducationRecord(record, i);
        }

        Logger.debug(`EducationService::validateEducationRecords::Validation completed for user ${classInst.userId}`);
    }

    private async validateSingleEducationRecord(record: EducationRecord, index: number): Promise<void> {
        const recordPrefix = `Education record ${index + 1}`;

        // Validate education type
        if (!record.educationType) {
            throw new Error(`${recordPrefix}: Education type is required`);
        }

        if (!isValidEducationType(record.educationType)) {
            throw new Error(`${recordPrefix}: Invalid education type ${record.educationType}`);
        }

        // Validate percentage if provided
        if (record.percentage !== undefined && record.percentage !== null) {
            if (!validatePercentage(record.percentage)) {
                throw new Error(`${recordPrefix}: Percentage must be between 0 and 100`);
            }
        }

        // Validate years if provided
        if (record.startYear !== undefined && record.startYear !== null) {
            if (!validateYear(record.startYear)) {
                throw new Error(`${recordPrefix}: Start year must be a valid year between 1950 and ${new Date().getFullYear() + 10}`);
            }
        }

        if (record.endYear !== undefined && record.endYear !== null) {
            if (!validateYear(record.endYear)) {
                throw new Error(`${recordPrefix}: End year must be a valid year between 1950 and ${new Date().getFullYear() + 10}`);
            }
        }

        // Validate year logic
        if (record.startYear && record.endYear && record.startYear >= record.endYear) {
            throw new Error(`${recordPrefix}: End year must be after start year`);
        }

        // Validate high school specific fields
        if (record.educationType === EDUCATION_TYPES.HIGH_SCHOOL) {
            if (record.educationStreamId !== undefined && record.educationStreamId !== null) {
                if (!Number.isInteger(record.educationStreamId) || record.educationStreamId <= 0) {
                    throw new Error(`${recordPrefix}: Education stream ID must be a positive integer`);
                }
                // Validate that the stream exists in education_streams table
                await this.validateEducationStreamExists(record.educationStreamId);
            }
        }

        // Validate certification specific fields
        if (record.educationType === EDUCATION_TYPES.CERTIFICATION) {
            if (record.expiryDate && record.expiryDate < new Date()) {
                Logger.warn(`${recordPrefix}: Certification has already expired`);
            }
        }

        // Validate required fields based on education type
        await this.validateRequiredFieldsByType(record, recordPrefix);
    }

    private async validateRequiredFieldsByType(record: EducationRecord, recordPrefix: string): Promise<void> {
        switch (record.educationType) {
            case EDUCATION_TYPES.HIGH_SCHOOL:
                // For high school, we might want to require institution name or stream
                break;
            case EDUCATION_TYPES.GRADUATION:
            case EDUCATION_TYPES.POST_GRADUATION:
                if (!record.degreeName) {
                    throw new Error(`${recordPrefix}: Degree name is required for graduation/post-graduation records`);
                }
                if (!record.institutionName) {
                    throw new Error(`${recordPrefix}: Institution name is required for graduation/post-graduation records`);
                }
                break;
            case EDUCATION_TYPES.CERTIFICATION:
                if (!record.degreeName) {
                    throw new Error(`${recordPrefix}: Certification name is required`);
                }
                if (!record.certificationAuthority) {
                    throw new Error(`${recordPrefix}: Certification authority is required`);
                }
                break;
            case EDUCATION_TYPES.DIPLOMA:
                if (!record.degreeName) {
                    throw new Error(`${recordPrefix}: Diploma name is required`);
                }
                break;
        }
    }

    private async validateEducationStreamExists(streamId: number): Promise<void> {
        try {
            // This would need to be implemented when we have the EducationStream model
            // For now, we'll assume the validation is done at the database level via foreign key constraint
            Logger.debug(`EducationService::validateEducationStreamExists::Validating stream ID ${streamId}`);
        } catch (error: any) {
            Logger.error(`EducationService::validateEducationStreamExists::Error: ${error.message}`);
            throw new Error('Invalid high school stream ID');
        }
    }

    private async saveEducationRecords(): Promise<void> {
        try {
            const dbModels = await PostgresModel.getDbModels();

            for (const record of this.educationRecords) {
                const educationData: Partial<UserEducationModelAttributes> = {
                    educationType: record.educationType,
                    institutionName: record.institutionName,
                    degreeName: record.degreeName,
                    specialization: record.specialization,
                    educationStreamId: record.educationStreamId,
                    percentage: record.percentage,
                    grade: record.grade,
                    startYear: record.startYear,
                    endYear: record.endYear,
                    isCompleted: record.isCompleted !== undefined ? record.isCompleted : true,
                    boardUniversity: record.boardUniversity,
                    location: record.location,
                    certificationAuthority: record.certificationAuthority,
                    certificateNumber: record.certificateNumber,
                    expiryDate: record.expiryDate,
                    notes: record.notes
                };

                // Filter out undefined values
                const filteredData: Partial<UserEducationModelAttributes> = {};
                Object.keys(educationData).forEach(key => {
                    const value = educationData[key as keyof UserEducationModelAttributes];
                    if (value !== undefined && value !== null) {
                        filteredData[key as keyof UserEducationModelAttributes] = value;
                    }
                });

                if (record.id) {
                    // Update existing record
                    await dbModels.userEducation.update(filteredData, {
                        id: record.id,
                        userId: this.userId
                    });
                } else {
                    // Create new record
                    await dbModels.userEducation.upsertUserEducation(this.userId, filteredData);
                }
            }

            Logger.debug(`EducationService::saveEducationRecords::Successfully saved ${this.educationRecords.length} education records for user ${this.userId}`);
        } catch (error: any) {
            Logger.error(`EducationService::saveEducationRecords::Error saving education records: ${error.message}`);
            throw error;
        }
    }

    public static async getUserEducation(userId: number): Promise<UserEducationModelAttributes[]> {
        try {
            const dbModels = await PostgresModel.getDbModels();
            return await dbModels.userEducation.getUserEducation(userId);
        } catch (error: any) {
            Logger.error(`EducationService::getUserEducation::Error: ${error.message}`);
            throw error;
        }
    }

    public static async getUserEducationByType(userId: number, educationType: EducationType): Promise<UserEducationModelAttributes | null> {
        try {
            const dbModels = await PostgresModel.getDbModels();
            return await dbModels.userEducation.getUserEducationByType(userId, educationType);
        } catch (error: any) {
            Logger.error(`EducationService::getUserEducationByType::Error: ${error.message}`);
            throw error;
        }
    }

    public static async deleteUserEducation(userId: number, educationId: number): Promise<void> {
        try {
            const dbModels = await PostgresModel.getDbModels();
            await dbModels.userEducation.deleteUserEducation(userId, educationId);
        } catch (error: any) {
            Logger.error(`EducationService::deleteUserEducation::Error: ${error.message}`);
            throw error;
        }
    }

    public static validateEducationRecords(educationData: any): EducationRecord[] {
        if (!Array.isArray(educationData)) {
            throw new Error('Education data must be an array of education records');
        }

        return educationData.map((record: any, index: number) => {
            const validRecord: EducationRecord = {
                educationType: record.educationType
            };

            // Validate and extract fields
            if (record.id !== undefined) validRecord.id = record.id;
            if (record.institutionName !== undefined) validRecord.institutionName = record.institutionName;
            if (record.degreeName !== undefined) validRecord.degreeName = record.degreeName;
            if (record.specialization !== undefined) validRecord.specialization = record.specialization;
            if (record.educationStreamId !== undefined) validRecord.educationStreamId = record.educationStreamId;
            if (record.percentage !== undefined) validRecord.percentage = record.percentage;
            if (record.grade !== undefined) validRecord.grade = record.grade;
            if (record.startYear !== undefined) validRecord.startYear = record.startYear;
            if (record.endYear !== undefined) validRecord.endYear = record.endYear;
            if (record.isCompleted !== undefined) validRecord.isCompleted = record.isCompleted;
            if (record.boardUniversity !== undefined) validRecord.boardUniversity = record.boardUniversity;
            if (record.location !== undefined) validRecord.location = record.location;
            if (record.certificationAuthority !== undefined) validRecord.certificationAuthority = record.certificationAuthority;
            if (record.certificateNumber !== undefined) validRecord.certificateNumber = record.certificateNumber;
            if (record.expiryDate !== undefined) validRecord.expiryDate = new Date(record.expiryDate);
            if (record.notes !== undefined) validRecord.notes = record.notes;

            return validRecord;
        });
    }

    // Helper method to create high school education record from legacy format
    public static createHighSchoolRecord(data: {
        educationStreamId?: number;
        tenthPercentage?: number;
        eleventhPercentage?: number;
        twelfthPercentage?: number;
        institutionName?: string;
        boardUniversity?: string;
    }): EducationRecord {
        return {
            educationType: EDUCATION_TYPES.HIGH_SCHOOL,
            educationStreamId: data.educationStreamId,
            percentage: data.twelfthPercentage, // Use 12th percentage as main percentage
            institutionName: data.institutionName,
            boardUniversity: data.boardUniversity,
            notes: data.tenthPercentage || data.eleventhPercentage ?
                `10th: ${data.tenthPercentage || 'N/A'}%, 11th: ${data.eleventhPercentage || 'N/A'}%, 12th: ${data.twelfthPercentage || 'N/A'}%` :
                undefined
        };
    }
}
