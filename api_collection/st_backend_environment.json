{"id": "8f9e1a2b-3c4d-5e6f-7890-abcdef123456", "name": "ST Backend Environment", "values": [{"key": "base_url", "value": "http://localhost:4023", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "temp_token", "value": "", "type": "secret", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "test_otp", "value": "", "type": "secret", "enabled": true}, {"key": "user_name", "value": "Test User", "type": "default", "enabled": true}, {"key": "user_password", "value": "password123", "type": "secret", "enabled": true}, {"key": "user_mobile", "value": "1234567890", "type": "default", "enabled": true}, {"key": "username", "value": "testuser", "type": "default", "enabled": true}, {"key": "assessment_id", "value": "1", "type": "default", "enabled": true}, {"key": "expert_id", "value": "1", "type": "default", "enabled": true}, {"key": "referral_code", "value": "SCHOOL123", "type": "default", "enabled": true}, {"key": "new_user_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "admin_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "admin_password", "value": "admin123", "type": "secret", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-01-13T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}