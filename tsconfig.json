{"compilerOptions": {"target": "es2022", "module": "commonjs", "strict": true, "esModuleInterop": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "alwaysStrict": true, "strictPropertyInitialization": false, "moduleResolution": "node", "typeRoots": ["./src/desc.d.ts"]}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "**/*.test.ts", "tmp"]}