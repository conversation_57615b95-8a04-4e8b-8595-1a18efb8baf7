# Education API Documentation

## Overview

The education system has been redesigned to support a **one-to-many relationship** between users and education records. This allows users to add multiple educational qualifications including high school, graduation, post-graduation, certifications, diplomas, and other educational achievements.

## Database Schema

### user_education Table

The `user_education` table stores multiple education records per user with the following structure:

- **id**: Primary key
- **user_id**: Foreign key to users table
- **education_type**: Integer representing type of education (1=high_school, 2=graduation, 3=post_graduation, 4=diploma, 5=certification, etc.)
- **institution_name**: Name of school/college/university
- **degree_name**: Name of degree/course/certification
- **specialization**: Stream/specialization (e.g., Computer Science, Science, Commerce)
- **education_stream_id**: Foreign key to education_streams table (for high school)
- **percentage**: Academic percentage/marks
- **grade**: Grade obtained (A+, B, First Class, etc.)
- **start_year**: Year when education started
- **end_year**: Year when education completed
- **is_completed**: Whether education is completed or ongoing
- **board_university**: Board/University name
- **location**: Location of institution
- **certification_authority**: Authority that issued certification
- **certificate_number**: Certificate number/ID
- **expiry_date**: Expiry date for certifications
- **notes**: Additional notes

## API Usage

### Update Profile with Education Records

**Endpoint**: `PUT /api/user/update-profile`

**Request Body**:
```json
{
  "name": "John Doe",
  "education": [
    {
      "educationType": 1,
      "institutionName": "ABC High School",
      "educationStreamId": 1,
      "percentage": 89.8,
      "boardUniversity": "CBSE",
      "location": "Delhi",
      "startYear": 2018,
      "endYear": 2020,
      "isCompleted": true,
      "notes": "10th: 85.5%, 11th: 87.2%, 12th: 89.8%"
    },
    {
      "educationType": 2,
      "institutionName": "XYZ Engineering College",
      "degreeName": "B.Tech",
      "specialization": "Computer Science",
      "percentage": 85.0,
      "boardUniversity": "Delhi University",
      "location": "Delhi",
      "startYear": 2020,
      "endYear": 2024,
      "isCompleted": true
    },
    {
      "educationType": 5,
      "degreeName": "AWS Certified Solutions Architect",
      "certificationAuthority": "Amazon Web Services",
      "certificateNumber": "AWS-CSA-2024-001",
      "startYear": 2024,
      "endYear": 2024,
      "expiryDate": "2027-12-31",
      "isCompleted": true
    }
  ]
}
```

## Education Types

Based on the existing `EducationConstants.ts`:

1. **HIGH_SCHOOL** (1): 10th/11th/12th standard
2. **GRADUATION** (2): Bachelor's degree
3. **POST_GRADUATION** (3): Master's degree
4. **DIPLOMA** (4): Diploma courses
5. **CERTIFICATION** (5): Professional certifications
6. **PROFESSIONAL_COURSE** (6): Professional courses
7. **ONLINE_COURSE** (7): Online courses
8. **SKILL_DEVELOPMENT** (8): Skill development programs

## Examples

### High School Record
```json
{
  "educationType": 1,
  "institutionName": "Delhi Public School",
  "educationStreamId": 1,
  "percentage": 89.8,
  "boardUniversity": "CBSE",
  "location": "Delhi",
  "startYear": 2018,
  "endYear": 2020,
  "isCompleted": true,
  "notes": "Science stream with PCM"
}
```

### Graduation Record
```json
{
  "educationType": 2,
  "institutionName": "IIT Delhi",
  "degreeName": "B.Tech",
  "specialization": "Computer Science and Engineering",
  "percentage": 85.0,
  "boardUniversity": "IIT Delhi",
  "location": "Delhi",
  "startYear": 2020,
  "endYear": 2024,
  "isCompleted": true
}
```

### Post Graduation Record
```json
{
  "educationType": 3,
  "institutionName": "IIT Bombay",
  "degreeName": "M.Tech",
  "specialization": "Artificial Intelligence",
  "percentage": 88.5,
  "boardUniversity": "IIT Bombay",
  "location": "Mumbai",
  "startYear": 2024,
  "endYear": 2026,
  "isCompleted": false
}
```

### Certification Record
```json
{
  "educationType": 5,
  "degreeName": "Google Cloud Professional Cloud Architect",
  "certificationAuthority": "Google Cloud",
  "certificateNumber": "GCP-PCA-2024-001",
  "startYear": 2024,
  "endYear": 2024,
  "expiryDate": "2026-12-31",
  "isCompleted": true,
  "notes": "Passed with 95% score"
}
```

### Diploma Record
```json
{
  "educationType": 4,
  "institutionName": "Polytechnic College",
  "degreeName": "Diploma in Computer Engineering",
  "specialization": "Computer Engineering",
  "percentage": 82.0,
  "location": "Mumbai",
  "startYear": 2018,
  "endYear": 2021,
  "isCompleted": true
}
```

## Validation Rules

1. **Education Type**: Must be a valid integer from EducationConstants
2. **High School**: Only one high school record allowed per user
3. **Percentage**: Must be between 0 and 100
4. **Years**: Must be valid years between 1950 and current year + 10
5. **Year Logic**: End year must be after start year
6. **Required Fields by Type**:
   - **High School**: No strict requirements (flexible)
   - **Graduation/Post-Graduation**: degreeName and institutionName required
   - **Certification**: degreeName and certificationAuthority required
   - **Diploma**: degreeName required

## Response Format

**Success Response**:
```json
{
  "success": true,
  "data": {
    "message": "Education details updated successfully",
    "recordsProcessed": 3,
    "educationTypes": [
      "High School",
      "Graduation", 
      "Certification"
    ]
  }
}
```

## Benefits of New Structure

1. **Flexibility**: Users can add multiple degrees, certifications, and courses
2. **Extensibility**: Easy to add new education types without schema changes
3. **Comprehensive**: Supports all types of educational qualifications
4. **Detailed**: Rich metadata for each education record
5. **Validation**: Type-specific validation rules
6. **Future-proof**: Designed to handle evolving education requirements

## Migration from Old Structure

The old structure with separate fields for 10th/11th/12th percentages can be migrated using the helper method:

```typescript
const highSchoolRecord = EducationService.createHighSchoolRecord({
  educationStreamId: 1,
  tenthPercentage: 85.5,
  eleventhPercentage: 87.2,
  twelfthPercentage: 89.8,
  institutionName: "ABC School",
  boardUniversity: "CBSE"
});
```

This creates a single high school record with the 12th percentage as the main percentage and other details in the notes field.
